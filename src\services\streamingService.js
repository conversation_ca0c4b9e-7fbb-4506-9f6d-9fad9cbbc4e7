const axios = require('axios');
const { v4: uuidv4 } = require('uuid');
const logger = require('../utils/logger');
const ResponseTranslator = require('../translators/responseTranslator');

/**
 * Handles streaming responses from OpenAI and translates them to Anthropic format
 */
class StreamingService {
  constructor(config) {
    this.config = config;
    this.responseTranslator = new ResponseTranslator(config);
  }

  /**
   * Handles streaming chat completion request
   */
  async handleStreamingRequest(openaiRequest, res, originalModel) {
    const messageId = `msg_${uuidv4().replace(/-/g, '')}`;
    let buffer = '';

    // Initialize stream state
    const streamState = {
      messageStartSent: false,
      contentBlockIndex: 0,
      contentBlockOpen: false,
      toolCalls: {}
    };

    try {
      // Set up SSE headers
      res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control'
      });

      // Create axios request for streaming
      const response = await axios({
        method: 'post',
        url: `${this.config.openai.base_url}/chat/completions`,
        headers: {
          'Authorization': `Bearer ${this.config.openai.api_key}`,
          'Content-Type': 'application/json'
        },
        data: openaiRequest,
        responseType: 'stream',
        timeout: this.config.openai.timeout || 30000
      });

      // Handle the streaming response
      response.data.on('data', (chunk) => {
        try {
          buffer += chunk.toString();
          const lines = buffer.split('\n');
          buffer = lines.pop() || ''; // Keep incomplete line in buffer

          for (const line of lines) {
            if (line.trim() === '') continue;
            if (line.trim() === 'data: [DONE]') {
              res.end();
              return;
            }

            if (line.startsWith('data: ')) {
              const data = line.slice(6);
              try {
                const chunk = JSON.parse(data);
                // Set the message ID and model for the first chunk
                if (!streamState.messageStartSent) {
                  chunk.id = messageId;
                  chunk.model = originalModel;
                }

                const events = this.translateChunkToAnthropicEvents(chunk, streamState);

                for (const event of events) {
                  this.writeEvent(res, event);
                }
              } catch (parseError) {
                logger.warn('Failed to parse chunk:', data);
              }
            }
          }
        } catch (error) {
          logger.error('Error processing chunk:', error);
          this.sendErrorEvent(res, error);
        }
      });

      response.data.on('end', () => {
        res.end();
      });

      response.data.on('error', (error) => {
        logger.error('Stream error:', error);
        this.sendErrorEvent(res, error);
        res.end();
      });

    } catch (error) {
      logger.error('Streaming request failed:', error);
      this.sendErrorEvent(res, error);
      res.end();
    }
  }

  /**
   * Check if a tool block is currently open
   */
  isToolBlockOpen(state) {
    if (!state.contentBlockOpen) {
      return false;
    }
    // Check if the current block index corresponds to any known tool call
    return Object.values(state.toolCalls).some(
      tc => tc.anthropicBlockIndex === state.contentBlockIndex
    );
  }

  /**
   * Translate OpenAI chunk to Anthropic events (based on working implementation)
   */
  translateChunkToAnthropicEvents(chunk, state) {
    const events = [];

    if (chunk.choices.length === 0) {
      return events;
    }

    const choice = chunk.choices[0];
    const { delta } = choice;

    if (!state.messageStartSent) {
      events.push({
        type: 'message_start',
        message: {
          id: chunk.id,
          type: 'message',
          role: 'assistant',
          content: [],
          model: chunk.model,
          stop_reason: null,
          stop_sequence: null,
          usage: {
            input_tokens: 1,
            output_tokens: 1 // Anthropic requires this to be > 0
          }
        }
      });
      state.messageStartSent = true;
    }

    if (delta.content) {
      if (this.isToolBlockOpen(state)) {
        // A tool block was open, so close it before starting a text block
        events.push({
          type: 'content_block_stop',
          index: state.contentBlockIndex
        });
        state.contentBlockIndex++;
        state.contentBlockOpen = false;
      }

      if (!state.contentBlockOpen) {
        events.push({
          type: 'content_block_start',
          index: state.contentBlockIndex,
          content_block: {
            type: 'text',
            text: ''
          }
        });
        state.contentBlockOpen = true;
      }

      events.push({
        type: 'content_block_delta',
        index: state.contentBlockIndex,
        delta: {
          type: 'text_delta',
          text: delta.content
        }
      });
    }

    if (delta.tool_calls) {
      for (const toolCall of delta.tool_calls) {
        if (toolCall.id && toolCall.function?.name) {
          // New tool call starting
          if (state.contentBlockOpen) {
            // Close any previously open block
            events.push({
              type: 'content_block_stop',
              index: state.contentBlockIndex
            });
            state.contentBlockIndex++;
            state.contentBlockOpen = false;
          }

          const anthropicBlockIndex = state.contentBlockIndex;
          state.toolCalls[toolCall.index] = {
            id: toolCall.id,
            name: toolCall.function.name,
            anthropicBlockIndex
          };

          events.push({
            type: 'content_block_start',
            index: anthropicBlockIndex,
            content_block: {
              type: 'tool_use',
              id: toolCall.id,
              name: toolCall.function.name,
              input: {}
            }
          });
          state.contentBlockOpen = true;
        }

        if (toolCall.function?.arguments) {
          const toolCallInfo = state.toolCalls[toolCall.index];
          if (toolCallInfo) {
            events.push({
              type: 'content_block_delta',
              index: toolCallInfo.anthropicBlockIndex,
              delta: {
                type: 'input_json_delta',
                partial_json: toolCall.function.arguments
              }
            });
          }
        }
      }
    }

    if (choice.finish_reason) {
      if (state.contentBlockOpen) {
        events.push({
          type: 'content_block_stop',
          index: state.contentBlockIndex
        });
        state.contentBlockOpen = false;
      }

      events.push({
        type: 'message_delta',
        delta: {
          stop_reason: this.responseTranslator.translateStopReason(choice.finish_reason),
          stop_sequence: null
        },
        usage: {
          output_tokens: 1
        }
      });

      events.push({
        type: 'message_stop'
      });
    }

    return events;
  }



  /**
   * Sends error event
   */
  sendErrorEvent(res, error) {
    const errorEvent = {
      type: 'error',
      error: {
        type: 'api_error',
        message: error.message || 'An error occurred during streaming'
      }
    };
    this.writeEvent(res, errorEvent);
  }

  /**
   * Writes an event to the SSE stream
   */
  writeEvent(res, event) {
    try {
      const eventString = `event: ${event.type}\ndata: ${JSON.stringify(event)}\n\n`;
      res.write(eventString);
    } catch (error) {
      logger.error('Failed to write event:', error);
    }
  }

  /**
   * Sends ping event to keep connection alive
   */
  sendPing(res) {
    const pingEvent = {
      type: 'ping'
    };
    this.writeEvent(res, pingEvent);
  }
}

module.exports = StreamingService;
