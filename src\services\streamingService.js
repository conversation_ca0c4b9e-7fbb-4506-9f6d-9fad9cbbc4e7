const axios = require('axios');
const { v4: uuidv4 } = require('uuid');
const logger = require('../utils/logger');
const ResponseTranslator = require('../translators/responseTranslator');

/**
 * Handles streaming responses from OpenAI and translates them to Anthropic format
 */
class StreamingService {
  constructor(config) {
    this.config = config;
    this.responseTranslator = new ResponseTranslator(config);
  }

  /**
   * Handles streaming chat completion request
   */
  async handleStreamingRequest(openaiRequest, res, originalModel) {
    const messageId = `msg_${uuidv4().replace(/-/g, '')}`;
    let isFirstChunk = true;
    let buffer = '';
    let contentBlockIndex = 0;
    let toolCallBuffer = {};

    try {
      // Set up SSE headers
      res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control'
      });

      // Create axios request for streaming
      const response = await axios({
        method: 'post',
        url: `${this.config.openai.base_url}/chat/completions`,
        headers: {
          'Authorization': `Bearer ${this.config.openai.api_key}`,
          'Content-Type': 'application/json'
        },
        data: openaiRequest,
        responseType: 'stream',
        timeout: this.config.openai.timeout || 30000
      });

      // Handle the streaming response
      response.data.on('data', (chunk) => {
        try {
          buffer += chunk.toString();
          const lines = buffer.split('\n');
          buffer = lines.pop() || ''; // Keep incomplete line in buffer

          for (const line of lines) {
            if (line.trim() === '') continue;
            if (line.trim() === 'data: [DONE]') {
              this.sendFinalEvents(res, messageId);
              res.end();
              return;
            }

            if (line.startsWith('data: ')) {
              const data = line.slice(6);
              try {
                const chunk = JSON.parse(data);
                this.processChunk(chunk, res, messageId, isFirstChunk, originalModel);
                isFirstChunk = false;
              } catch (parseError) {
                logger.warn('Failed to parse chunk:', data);
              }
            }
          }
        } catch (error) {
          logger.error('Error processing chunk:', error);
          this.sendErrorEvent(res, error);
        }
      });

      response.data.on('end', () => {
        if (!res.headersSent) {
          this.sendFinalEvents(res, messageId);
        }
        res.end();
      });

      response.data.on('error', (error) => {
        logger.error('Stream error:', error);
        this.sendErrorEvent(res, error);
        res.end();
      });

    } catch (error) {
      logger.error('Streaming request failed:', error);
      this.sendErrorEvent(res, error);
      res.end();
    }
  }

  /**
   * Processes individual chunks from OpenAI stream
   */
  processChunk(openaiChunk, res, messageId, isFirst, originalModel) {
    if (isFirst) {
      // Send message_start event with exact Anthropic format
      const messageStartEvent = {
        type: 'message_start',
        message: {
          id: messageId,
          type: 'message',
          role: 'assistant',
          content: [],
          model: originalModel,
          stop_reason: null,
          stop_sequence: null,
          usage: {
            input_tokens: 0,
            output_tokens: 0,
            cache_creation_input_tokens: 0,
            cache_read_input_tokens: 0
          }
        }
      };
      this.writeEvent(res, messageStartEvent);

      // Send content_block_start for text
      const contentBlockStartEvent = {
        type: 'content_block_start',
        index: 0,
        content_block: {
          type: 'text',
          text: ''
        }
      };
      this.writeEvent(res, contentBlockStartEvent);
    }

    const choice = openaiChunk.choices?.[0];
    if (!choice) return;

    // Handle text content
    if (choice.delta?.content) {
      const textDeltaEvent = {
        type: 'content_block_delta',
        index: 0,
        delta: {
          type: 'text_delta',
          text: choice.delta.content
        }
      };
      this.writeEvent(res, textDeltaEvent);
    }

    // Handle tool calls
    if (choice.delta?.tool_calls) {
      this.processToolCalls(choice.delta.tool_calls, res);
    }

    // Handle finish reason
    if (choice.finish_reason) {
      this.sendFinalEvents(res, messageId, choice.finish_reason, openaiChunk.usage);
    }
  }

  /**
   * Processes tool calls in streaming chunks
   */
  processToolCalls(toolCalls, res) {
    for (const toolCall of toolCalls) {
      const index = toolCall.index || 0;
      
      if (toolCall.function?.name) {
        // Start of new tool call
        const toolStartEvent = {
          type: 'content_block_start',
          index: index + 1, // Offset by 1 since text is at index 0
          content_block: {
            type: 'tool_use',
            id: toolCall.id,
            name: toolCall.function.name,
            input: {}
          }
        };
        this.writeEvent(res, toolStartEvent);
      }

      if (toolCall.function?.arguments) {
        // Tool arguments delta
        const toolDeltaEvent = {
          type: 'content_block_delta',
          index: index + 1,
          delta: {
            type: 'input_json_delta',
            partial_json: toolCall.function.arguments
          }
        };
        this.writeEvent(res, toolDeltaEvent);
      }
    }
  }

  /**
   * Sends final events to complete the stream
   */
  sendFinalEvents(res, messageId, finishReason = 'stop', usage = null) {
    // Send content_block_stop for text
    const contentBlockStopEvent = {
      type: 'content_block_stop',
      index: 0
    };
    this.writeEvent(res, contentBlockStopEvent);

    // Send message_delta with final information
    const messageDeltaEvent = {
      type: 'message_delta',
      delta: {
        stop_reason: this.responseTranslator.translateStopReason(finishReason),
        stop_sequence: null
      }
    };

    if (usage) {
      messageDeltaEvent.usage = {
        output_tokens: usage.completion_tokens || 0
      };
    }

    this.writeEvent(res, messageDeltaEvent);

    // Send message_stop
    const messageStopEvent = {
      type: 'message_stop'
    };
    this.writeEvent(res, messageStopEvent);
  }

  /**
   * Sends error event
   */
  sendErrorEvent(res, error) {
    const errorEvent = {
      type: 'error',
      error: {
        type: 'api_error',
        message: error.message || 'An error occurred during streaming'
      }
    };
    this.writeEvent(res, errorEvent);
  }

  /**
   * Writes an event to the SSE stream
   */
  writeEvent(res, event) {
    try {
      const eventString = `event: ${event.type}\ndata: ${JSON.stringify(event)}\n\n`;
      res.write(eventString);
    } catch (error) {
      logger.error('Failed to write event:', error);
    }
  }

  /**
   * Sends ping event to keep connection alive
   */
  sendPing(res) {
    const pingEvent = {
      type: 'ping'
    };
    this.writeEvent(res, pingEvent);
  }
}

module.exports = StreamingService;
