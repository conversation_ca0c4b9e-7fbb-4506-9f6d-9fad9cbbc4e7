{"openai": {"api_key": "********************************************************", "base_url": "https://api.openai.com/v1", "timeout": 30000, "max_retries": 3}, "server": {"host": "localhost", "port": 4141, "cors": {"enabled": true, "origins": ["*"]}}, "logging": {"level": "info", "enable_request_logging": true, "enable_response_logging": false}, "rate_limiting": {"enabled": false, "requests_per_minute": 60, "burst_limit": 10}, "model_mapping": {"enabled": false}, "features": {"streaming": true, "tool_calling": true, "vision": true, "token_counting": true}, "auth": {"validate_keys": false, "allowed_keys": []}}