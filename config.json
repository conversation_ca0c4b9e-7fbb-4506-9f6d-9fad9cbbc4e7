{"openai": {"api_key": "********************************************************", "base_url": "https://api.openai.com/v1", "timeout": 30000, "max_retries": 3}, "server": {"host": "localhost", "port": 3000, "cors": {"enabled": true, "origins": ["*"]}}, "logging": {"level": "info", "enable_request_logging": true, "enable_response_logging": false}, "rate_limiting": {"enabled": false, "requests_per_minute": 60, "burst_limit": 10}, "model_mapping": {"claude-3-5-sonnet-20241022": "gpt-4o", "claude-3-5-haiku-20241022": "gpt-4o-mini", "claude-3-opus-20240229": "gpt-4-turbo", "claude-3-sonnet-20240229": "gpt-4", "claude-3-haiku-20240307": "gpt-3.5-turbo"}, "features": {"streaming": true, "tool_calling": true, "vision": true, "token_counting": true}, "auth": {"validate_keys": false, "allowed_keys": []}}