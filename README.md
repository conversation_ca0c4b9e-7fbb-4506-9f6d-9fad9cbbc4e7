# OpenAI-Anthropic API Proxy

A proxy server that translates between OpenAI and Anthropic APIs, allowing you to use OpenAI models through Anthropic-compatible endpoints.

## Features

- **API Translation**: Converts Anthropic API requests to OpenAI format and responses back to Anthropic format
- **Streaming Support**: Full support for Server-Sent Events (SSE) streaming
- **Tool/Function Calling**: Complete tool calling support with proper translation
- **Model Mapping**: Configurable mapping between Anthropic and OpenAI model names
- **Authentication**: Anthropic-style API key authentication
- **Error Handling**: Comprehensive error handling with proper HTTP status codes
- **Logging**: Detailed logging with configurable levels
- **Rate Limiting**: Optional rate limiting support

## Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <repository-url>
cd openai-anthropic-proxy

# Install dependencies
npm install
```

### 2. Configuration

#### Option A: Interactive Setup (Recommended)

```bash
npm run setup
```

This will guide you through the configuration process interactively.

#### Option B: Manual Configuration

Copy the template configuration and update with your OpenAI credentials:

```bash
cp config.json config.json.backup
```

Edit `config.json`:

```json
{
  "openai": {
    "api_key": "sk-your-openai-api-key-here",
    "base_url": "https://api.openai.com/v1"
  }
}
```

### 3. Start the Server

```bash
# Development mode with auto-reload
npm run dev

# Production mode
npm start
```

The server will start on `http://localhost:3000` by default.

### 4. Test the Proxy

```bash
# Run the test suite to verify everything is working
npm run test:proxy
```

## Usage Examples

### Basic Chat Completion

```bash
curl -X POST http://localhost:3000/v1/messages \
  -H "Content-Type: application/json" \
  -H "x-api-key: your-api-key" \
  -H "anthropic-version: 2023-06-01" \
  -d '{
    "model": "claude-3-5-sonnet-20241022",
    "max_tokens": 1024,
    "messages": [
      {
        "role": "user",
        "content": "Hello, how are you?"
      }
    ]
  }'
```

### Streaming Response

```bash
curl -X POST http://localhost:3000/v1/messages \
  -H "Content-Type: application/json" \
  -H "x-api-key: your-api-key" \
  -H "anthropic-version: 2023-06-01" \
  -d '{
    "model": "claude-3-5-sonnet-20241022",
    "max_tokens": 1024,
    "stream": true,
    "messages": [
      {
        "role": "user",
        "content": "Tell me a story"
      }
    ]
  }'
```

### Tool/Function Calling

```bash
curl -X POST http://localhost:3000/v1/messages \
  -H "Content-Type: application/json" \
  -H "x-api-key: your-api-key" \
  -H "anthropic-version: 2023-06-01" \
  -d '{
    "model": "claude-3-5-sonnet-20241022",
    "max_tokens": 1024,
    "tools": [
      {
        "name": "get_weather",
        "description": "Get weather information",
        "input_schema": {
          "type": "object",
          "properties": {
            "location": {"type": "string"}
          },
          "required": ["location"]
        }
      }
    ],
    "messages": [
      {
        "role": "user",
        "content": "What's the weather in San Francisco?"
      }
    ]
  }'
```

### List Models

```bash
curl -X GET http://localhost:3000/v1/models \
  -H "x-api-key: your-api-key"
```

### Count Tokens

```bash
curl -X POST http://localhost:3000/v1/messages/count_tokens \
  -H "Content-Type: application/json" \
  -H "x-api-key: your-api-key" \
  -d '{
    "model": "claude-3-5-sonnet-20241022",
    "messages": [
      {
        "role": "user",
        "content": "Hello, how are you?"
      }
    ]
  }'
```

## Configuration

The `config.json` file supports the following options:

### OpenAI Configuration
- `api_key`: Your OpenAI API key (required)
- `base_url`: OpenAI API base URL (default: "https://api.openai.com/v1")
- `timeout`: Request timeout in milliseconds (default: 30000)
- `max_retries`: Maximum number of retries for failed requests (default: 3)

### Server Configuration
- `host`: Server host (default: "localhost")
- `port`: Server port (default: 3000)
- `cors`: CORS configuration

### Model Mapping
Configure which OpenAI models to use for each Anthropic model:

```json
{
  "model_mapping": {
    "claude-3-5-sonnet-20241022": "gpt-4o",
    "claude-3-5-haiku-20241022": "gpt-4o-mini",
    "claude-3-opus-20240229": "gpt-4-turbo"
  }
}
```

### Logging
- `level`: Log level (debug, info, warn, error)
- `enable_request_logging`: Log incoming requests
- `enable_response_logging`: Log outgoing responses

### Rate Limiting
- `enabled`: Enable rate limiting
- `requests_per_minute`: Maximum requests per minute
- `burst_limit`: Burst limit for rate limiting

## API Compatibility

This proxy implements the following Anthropic API endpoints:

- `POST /v1/messages` - Create a message
- `POST /v1/messages/count_tokens` - Count tokens
- `GET /v1/models` - List models
- `GET /v1/models/{model_id}` - Get model info

## Translation Details

### Request Translation
- Anthropic `system` parameter → OpenAI system message
- Anthropic `max_tokens` → OpenAI `max_tokens`
- Anthropic `stop_sequences` → OpenAI `stop`
- Anthropic `tools` → OpenAI `tools` with function format
- Anthropic `tool_choice` → OpenAI `tool_choice`

### Response Translation
- OpenAI `choices[0].message` → Anthropic `content` array
- OpenAI `finish_reason` → Anthropic `stop_reason`
- OpenAI `usage` → Anthropic `usage` with different field names
- OpenAI tool calls → Anthropic `tool_use` content blocks

### Streaming Translation
- OpenAI chunk-based streaming → Anthropic structured events
- Generates proper `message_start`, `content_block_start`, `content_block_delta`, and `message_stop` events

## Error Handling

The proxy translates OpenAI errors to Anthropic format:

- `400` → `invalid_request_error`
- `401` → `authentication_error`
- `403` → `permission_error`
- `429` → `rate_limit_error`
- `500+` → `api_error`

## Development

```bash
# Install dependencies
npm install

# Run in development mode
npm run dev

# Run tests
npm test

# Lint code
npm run lint

# Format code
npm run format
```

## License

MIT License
