const express = require('express');
const axios = require('axios');
const logger = require('../utils/logger');
const ResponseTranslator = require('../translators/responseTranslator');

/**
 * Creates the models router for handling model-related endpoints
 */
function createModelsRouter(config) {
  const router = express.Router();
  const responseTranslator = new ResponseTranslator(config);
  
  // Create reverse mapping for model translation
  const reverseModelMapping = {};
  for (const [anthropicModel, openaiModel] of Object.entries(config.model_mapping)) {
    reverseModelMapping[anthropicModel] = openaiModel;
  }

  /**
   * GET /v1/models - List available models
   */
  router.get('/', async (req, res, next) => {
    try {
      logger.info('Fetching models list from OpenAI');

      const response = await axios.get(`${config.openai.base_url}/models`, {
        headers: {
          'Authorization': `Bearer ${config.openai.api_key}`
        },
        timeout: config.openai.timeout || 30000
      });

      const anthropicResponse = responseTranslator.translateModelsListResponse(
        response.data, 
        reverseModelMapping
      );

      logger.debug('Models list translated successfully');
      res.json(anthropicResponse);

    } catch (error) {
      logger.error('Failed to fetch models list:', error.message);
      next(error);
    }
  });

  /**
   * GET /v1/models/{model_id} - Get specific model information
   */
  router.get('/:model_id', async (req, res, next) => {
    try {
      const anthropicModelId = req.params.model_id;
      const openaiModelId = reverseModelMapping[anthropicModelId];

      if (!openaiModelId) {
        return res.status(404).json({
          type: 'error',
          error: {
            type: 'not_found_error',
            message: `Model ${anthropicModelId} not found`
          }
        });
      }

      logger.info(`Fetching model info for ${anthropicModelId} (mapped to ${openaiModelId})`);

      const response = await axios.get(`${config.openai.base_url}/models/${openaiModelId}`, {
        headers: {
          'Authorization': `Bearer ${config.openai.api_key}`
        },
        timeout: config.openai.timeout || 30000
      });

      const anthropicResponse = responseTranslator.translateModelResponse(
        response.data,
        anthropicModelId
      );

      logger.debug('Model info translated successfully');
      res.json(anthropicResponse);

    } catch (error) {
      if (error.response?.status === 404) {
        return res.status(404).json({
          type: 'error',
          error: {
            type: 'not_found_error',
            message: `Model ${req.params.model_id} not found`
          }
        });
      }

      logger.error('Failed to fetch model info:', error.message);
      next(error);
    }
  });

  return router;
}

module.exports = { createModelsRouter };
