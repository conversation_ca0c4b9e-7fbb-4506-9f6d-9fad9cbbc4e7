{"name": "openai-anthropic-proxy", "version": "1.0.0", "description": "A proxy server that translates between OpenAI and Anthropic APIs", "main": "src/server.js", "scripts": {"setup": "node setup.js", "start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:proxy": "node examples/test-proxy.js", "troubleshoot": "node troubleshoot.js", "models": "node troubleshoot.js --models", "test:claude": "node test-claude-cli.js", "debug:claude": "node test-claude-cli.js --debug", "test:anthropic": "ANTHROPIC_MODEL=gpt-4o node test-claude-cli.js", "debug:server": "node debug-server.js", "test:simple": "node simple-test.js", "test:network": "node simple-test.js --network", "test:models": "node test-model-validation.js", "debug:json": "node debug-json.js", "test:direct": "node test-direct-openai.js", "check:o3": "node check-o3-model.js", "lint": "eslint src/", "format": "prettier --write src/"}, "keywords": ["openai", "anthropic", "proxy", "api", "translation"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "axios": "^1.6.2", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "eslint": "^8.55.0", "prettier": "^3.1.1"}, "engines": {"node": ">=16.0.0"}}