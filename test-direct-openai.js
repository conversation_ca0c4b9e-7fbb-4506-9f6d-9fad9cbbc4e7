#!/usr/bin/env node

/**
 * Test direct OpenAI API call to isolate the JSON issue
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Load config
const configPath = path.join(__dirname, 'config.json');
const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));

async function testDirectOpenAI() {
  console.log('🔍 Testing direct OpenAI API call...');
  
  const requestData = {
    model: 'gpt-4o',
    messages: [
      {
        role: 'user',
        content: 'Say hello'
      }
    ],
    max_tokens: 10
  };

  console.log('Request data:', JSON.stringify(requestData, null, 2));

  try {
    const response = await axios.post(`${config.openai.base_url}/chat/completions`, requestData, {
      headers: {
        'Authorization': `Bearer ${config.openai.api_key}`,
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });

    console.log('✅ Direct OpenAI call successful');
    console.log('Response:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Direct OpenAI call failed');
    console.log('Status:', error.response?.status);
    console.log('Error:', error.response?.data || error.message);
    return false;
  }
}

async function testProxyTranslation() {
  console.log('\n🔍 Testing proxy translation...');
  
  const RequestTranslator = require('./src/translators/requestTranslator');
  const translator = new RequestTranslator(config);

  const anthropicRequest = {
    model: 'gpt-4o',
    max_tokens: 10,
    messages: [
      {
        role: 'user',
        content: 'Say hello'
      }
    ]
  };

  try {
    const openaiRequest = translator.translateMessagesRequest(anthropicRequest);
    console.log('✅ Translation successful');
    console.log('Translated request:', JSON.stringify(openaiRequest, null, 2));

    // Test the translated request directly with OpenAI
    console.log('\n🔍 Testing translated request with OpenAI...');
    const response = await axios.post(`${config.openai.base_url}/chat/completions`, openaiRequest, {
      headers: {
        'Authorization': `Bearer ${config.openai.api_key}`,
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });

    console.log('✅ Translated request works with OpenAI');
    console.log('Response:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Translation or OpenAI call failed');
    console.log('Error:', error.message);
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Response:', error.response.data);
    }
    return false;
  }
}

async function testProxyEndpoint() {
  console.log('\n🔍 Testing proxy endpoint...');
  
  const anthropicRequest = {
    model: 'gpt-4o',
    max_tokens: 10,
    messages: [
      {
        role: 'user',
        content: 'Say hello'
      }
    ]
  };

  try {
    const response = await axios.post('http://localhost:4141/v1/messages', anthropicRequest, {
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': 'dummy',
        'anthropic-version': '2023-06-01'
      },
      timeout: 30000
    });

    console.log('✅ Proxy endpoint successful');
    console.log('Response:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Proxy endpoint failed');
    console.log('Status:', error.response?.status);
    console.log('Error:', error.response?.data || error.message);
    return false;
  }
}

async function main() {
  console.log('🧪 Testing JSON issue isolation...\n');
  
  const results = await Promise.all([
    testDirectOpenAI(),
    testProxyTranslation(),
    testProxyEndpoint()
  ]);

  const [directOK, translationOK, proxyOK] = results;

  console.log('\n📊 Test Results:');
  console.log(`Direct OpenAI: ${directOK ? '✅' : '❌'}`);
  console.log(`Translation: ${translationOK ? '✅' : '❌'}`);
  console.log(`Proxy Endpoint: ${proxyOK ? '✅' : '❌'}`);

  if (directOK && !translationOK) {
    console.log('\n🔍 Issue is in the request translation logic');
  } else if (directOK && translationOK && !proxyOK) {
    console.log('\n🔍 Issue is in the proxy HTTP handling');
  } else if (!directOK) {
    console.log('\n🔍 Issue is with OpenAI API configuration or credentials');
  } else {
    console.log('\n🎉 All tests passed - issue might be intermittent');
  }
}

if (require.main === module) {
  main().catch(console.error);
}
