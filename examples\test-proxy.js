#!/usr/bin/env node

/**
 * Simple test script for the OpenAI-Anthropic proxy
 */

const axios = require('axios');

const PROXY_URL = process.env.PROXY_URL || 'http://localhost:3000';
const API_KEY = process.env.API_KEY || 'test-key';

async function testBasicMessage() {
  console.log('Testing basic message...');
  
  try {
    const response = await axios.post(`${PROXY_URL}/v1/messages`, {
      model: 'claude-3-5-sonnet-20241022',
      max_tokens: 100,
      messages: [
        {
          role: 'user',
          content: 'Hello! Please respond with a short greeting.'
        }
      ]
    }, {
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01'
      }
    });

    console.log('✅ Basic message test passed');
    console.log('Response:', JSON.stringify(response.data, null, 2));
    return true;
  } catch (error) {
    console.log('❌ Basic message test failed');
    console.error('Error:', error.response?.data || error.message);
    return false;
  }
}

async function testModels() {
  console.log('\nTesting models endpoint...');
  
  try {
    const response = await axios.get(`${PROXY_URL}/v1/models`, {
      headers: {
        'x-api-key': API_KEY
      }
    });

    console.log('✅ Models test passed');
    console.log('Available models:', response.data.data.map(m => m.id));
    return true;
  } catch (error) {
    console.log('❌ Models test failed');
    console.error('Error:', error.response?.data || error.message);
    return false;
  }
}

async function testTokenCount() {
  console.log('\nTesting token count...');
  
  try {
    const response = await axios.post(`${PROXY_URL}/v1/messages/count_tokens`, {
      model: 'claude-3-5-sonnet-20241022',
      messages: [
        {
          role: 'user',
          content: 'Hello! This is a test message for token counting.'
        }
      ]
    }, {
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY
      }
    });

    console.log('✅ Token count test passed');
    console.log('Token count:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Token count test failed');
    console.error('Error:', error.response?.data || error.message);
    return false;
  }
}

async function testHealth() {
  console.log('\nTesting health endpoint...');
  
  try {
    const response = await axios.get(`${PROXY_URL}/health`);
    console.log('✅ Health test passed');
    console.log('Health status:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Health test failed');
    console.error('Error:', error.response?.data || error.message);
    return false;
  }
}

async function runTests() {
  console.log(`Testing OpenAI-Anthropic Proxy at ${PROXY_URL}\n`);
  
  const results = await Promise.all([
    testHealth(),
    testModels(),
    testTokenCount(),
    testBasicMessage()
  ]);

  const passed = results.filter(r => r).length;
  const total = results.length;

  console.log(`\n📊 Test Results: ${passed}/${total} tests passed`);
  
  if (passed === total) {
    console.log('🎉 All tests passed! The proxy is working correctly.');
    process.exit(0);
  } else {
    console.log('⚠️  Some tests failed. Please check the configuration and logs.');
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests().catch(error => {
    console.error('Test runner failed:', error);
    process.exit(1);
  });
}

module.exports = { testBasicMessage, testModels, testTokenCount, testHealth };
