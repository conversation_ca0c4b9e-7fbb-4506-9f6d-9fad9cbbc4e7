const { v4: uuidv4 } = require('uuid');
const logger = require('../utils/logger');

/**
 * Translates OpenAI API responses to Anthropic API format
 */
class ResponseTranslator {
  constructor(config) {
    this.config = config;
  }

  /**
   * Main translation method for chat completion responses
   */
  translateChatCompletionResponse(openaiResponse, originalModel) {
    const choice = openaiResponse.choices[0];
    const textBlocks = this.getAnthropicTextBlocks(choice.message.content);
    const toolUseBlocks = this.getAnthropicToolUseBlocks(choice.message.tool_calls);

    const anthropicResponse = {
      id: openaiResponse.id || `msg_${uuidv4().replace(/-/g, '')}`,
      type: 'message',
      role: 'assistant',
      model: originalModel,
      content: [...textBlocks, ...toolUseBlocks],
      stop_reason: this.translateStopReason(choice.finish_reason),
      stop_sequence: null,
      usage: {
        input_tokens: openaiResponse.usage?.prompt_tokens ?? 0,
        output_tokens: openaiResponse.usage?.completion_tokens ?? 0,
        cache_creation_input_tokens: 0,
        cache_read_input_tokens: 0
      }
    };

    logger.debug('Translated response:', { openaiResponse, anthropicResponse });
    return anthropicResponse;
  }

  /**
   * Get Anthropic text blocks from OpenAI message content
   */
  getAnthropicTextBlocks(messageContent) {
    if (typeof messageContent === 'string') {
      return [{ type: 'text', text: messageContent }];
    }

    if (Array.isArray(messageContent)) {
      return messageContent
        .filter(part => part.type === 'text')
        .map(part => ({ type: 'text', text: part.text }));
    }

    return [];
  }

  /**
   * Get Anthropic tool use blocks from OpenAI tool calls
   */
  getAnthropicToolUseBlocks(toolCalls) {
    if (!toolCalls) {
      return [];
    }
    return toolCalls.map(toolCall => ({
      type: 'tool_use',
      id: toolCall.id,
      name: toolCall.function.name,
      input: JSON.parse(toolCall.function.arguments)
    }));
  }

  /**
   * Translates stop reason from OpenAI to Anthropic format
   */
  translateStopReason(finishReason) {
    if (finishReason === null) {
      return null;
    }
    const stopReasonMap = {
      'stop': 'end_turn',
      'length': 'max_tokens',
      'tool_calls': 'tool_use',
      'content_filter': 'end_turn'
    };
    return stopReasonMap[finishReason] || 'end_turn';
  }

  /**
   * Translates usage statistics from OpenAI to Anthropic format
   */
  translateUsage(openaiUsage) {
    if (!openaiUsage) {
      return {
        input_tokens: 0,
        output_tokens: 0
      };
    }

    return {
      input_tokens: openaiUsage.prompt_tokens || 0,
      output_tokens: openaiUsage.completion_tokens || 0,
      cache_creation_input_tokens: 0,
      cache_read_input_tokens: 0
    };
  }

  /**
   * Translates streaming chunk from OpenAI to Anthropic format
   */
  translateStreamingChunk(openaiChunk, messageId, isFirst = false, isLast = false) {
    const events = [];

    if (isFirst) {
      // Send message_start event
      events.push({
        type: 'message_start',
        message: {
          id: messageId,
          type: 'message',
          role: 'assistant',
          content: [],
          model: openaiChunk.model,
          stop_reason: null,
          stop_sequence: null,
          usage: {
            input_tokens: 0,
            output_tokens: 0
          }
        }
      });

      // Send content_block_start event
      events.push({
        type: 'content_block_start',
        index: 0,
        content_block: {
          type: 'text',
          text: ''
        }
      });
    }

    const choice = openaiChunk.choices[0];
    if (choice?.delta?.content) {
      // Send content_block_delta event
      events.push({
        type: 'content_block_delta',
        index: 0,
        delta: {
          type: 'text_delta',
          text: choice.delta.content
        }
      });
    }

    if (choice?.delta?.tool_calls) {
      // Handle tool calls in streaming
      for (const toolCall of choice.delta.tool_calls) {
        if (toolCall.function?.name) {
          // Start of tool call
          events.push({
            type: 'content_block_start',
            index: 1,
            content_block: {
              type: 'tool_use',
              id: toolCall.id,
              name: toolCall.function.name,
              input: {}
            }
          });
        }

        if (toolCall.function?.arguments) {
          // Tool arguments delta
          events.push({
            type: 'content_block_delta',
            index: 1,
            delta: {
              type: 'input_json_delta',
              partial_json: toolCall.function.arguments
            }
          });
        }
      }
    }

    if (isLast || choice?.finish_reason) {
      // Send content_block_stop event
      events.push({
        type: 'content_block_stop',
        index: 0
      });

      // Send message_delta event
      events.push({
        type: 'message_delta',
        delta: {
          stop_reason: this.translateStopReason(choice?.finish_reason),
          stop_sequence: null
        },
        usage: openaiChunk.usage ? this.translateUsage(openaiChunk.usage) : undefined
      });

      // Send message_stop event
      events.push({
        type: 'message_stop'
      });
    }

    return events;
  }

  /**
   * Formats streaming event for SSE
   */
  formatStreamingEvent(event) {
    return `event: ${event.type}\ndata: ${JSON.stringify(event)}\n\n`;
  }

  /**
   * Translates model list response from OpenAI to Anthropic format
   */
  translateModelsListResponse(openaiModels) {
    const anthropicModels = openaiModels.data.map(model => ({
      id: model.id,
      type: 'model',
      display_name: this.getDisplayName(model.id),
      created_at: new Date(model.created * 1000).toISOString()
    }));

    return {
      data: anthropicModels,
      has_more: false
    };
  }

  /**
   * Translates single model response from OpenAI to Anthropic format
   */
  translateModelResponse(openaiModel) {
    return {
      id: openaiModel.id,
      type: 'model',
      display_name: this.getDisplayName(openaiModel.id),
      created_at: new Date(openaiModel.created * 1000).toISOString()
    };
  }

  /**
   * Gets display name for OpenAI model
   */
  getDisplayName(modelId) {
    // Common OpenAI model display names
    const displayNames = {
      'gpt-4o': 'GPT-4o',
      'gpt-4o-mini': 'GPT-4o Mini',
      'gpt-4-turbo': 'GPT-4 Turbo',
      'gpt-4': 'GPT-4',
      'gpt-3.5-turbo': 'GPT-3.5 Turbo',
      'gpt-3.5-turbo-16k': 'GPT-3.5 Turbo 16K',
      'text-embedding-ada-002': 'Text Embedding Ada 002',
      'text-embedding-3-small': 'Text Embedding 3 Small',
      'text-embedding-3-large': 'Text Embedding 3 Large',
      'dall-e-2': 'DALL-E 2',
      'dall-e-3': 'DALL-E 3',
      'whisper-1': 'Whisper',
      'tts-1': 'TTS 1',
      'tts-1-hd': 'TTS 1 HD'
    };

    return displayNames[modelId] || this.formatModelName(modelId);
  }

  /**
   * Formats model name for display
   */
  formatModelName(modelId) {
    // Convert model ID to a more readable format
    return modelId
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }
}

module.exports = ResponseTranslator;
