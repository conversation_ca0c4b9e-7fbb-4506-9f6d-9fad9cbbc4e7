#!/usr/bin/env node

/**
 * Simple connectivity test to isolate the issue
 */

const http = require('http');
const https = require('https');

const PROXY_URL = process.env.ANTHROPIC_BASE_URL || 'http://localhost:3000';
const API_KEY = process.env.ANTHROPIC_AUTH_TOKEN || 'dummy';

function makeRequest(url, options, data) {
  return new Promise((resolve, reject) => {
    const client = url.startsWith('https:') ? https : http;
    
    const req = client.request(url, options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: body
        });
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(data);
    }
    req.end();
  });
}

async function testBasicConnectivity() {
  console.log('🔍 Testing basic connectivity...\n');
  
  // Test 1: Basic HTTP connection
  console.log('1. Testing basic HTTP connection...');
  try {
    const response = await makeRequest(`${PROXY_URL}/health`, {
      method: 'GET',
      timeout: 5000
    });
    
    console.log(`✅ Connection successful - Status: ${response.statusCode}`);
    console.log(`   Response: ${response.body}`);
  } catch (error) {
    console.log(`❌ Connection failed: ${error.message}`);
    console.log(`   Error code: ${error.code}`);
    console.log(`   Error details:`, error);
    return false;
  }

  // Test 2: Test /v1/messages endpoint
  console.log('\n2. Testing /v1/messages endpoint...');
  try {
    const requestData = JSON.stringify({
      model: 'gpt-4o',
      max_tokens: 10,
      messages: [
        {
          role: 'user',
          content: 'Hi'
        }
      ]
    });

    const response = await makeRequest(`${PROXY_URL}/v1/messages`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(requestData),
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01'
      },
      timeout: 30000
    }, requestData);

    console.log(`✅ Messages endpoint - Status: ${response.statusCode}`);
    console.log(`   Response: ${response.body.substring(0, 200)}...`);
    
    if (response.statusCode === 200) {
      try {
        const parsed = JSON.parse(response.body);
        console.log(`   Parsed response type: ${parsed.type}`);
        console.log(`   Content blocks: ${parsed.content?.length || 0}`);
      } catch (e) {
        console.log(`   Could not parse response as JSON`);
      }
    }
    
  } catch (error) {
    console.log(`❌ Messages endpoint failed: ${error.message}`);
    console.log(`   Error code: ${error.code}`);
    return false;
  }

  // Test 3: Test what Claude CLI might be doing
  console.log('\n3. Testing Claude CLI style request...');
  try {
    const requestData = JSON.stringify({
      model: 'gpt-4o',
      max_tokens: 50,
      messages: [
        {
          role: 'user',
          content: 'Say hello'
        }
      ]
    });

    const response = await makeRequest(`${PROXY_URL}/v1/messages`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(requestData),
        'Authorization': `Bearer ${API_KEY}`,
        'anthropic-version': '2023-06-01',
        'User-Agent': 'claude-cli/test'
      },
      timeout: 30000
    }, requestData);

    console.log(`✅ Claude CLI style - Status: ${response.statusCode}`);
    if (response.statusCode === 200) {
      console.log(`   Success! Response received.`);
    } else {
      console.log(`   Response: ${response.body}`);
    }
    
  } catch (error) {
    console.log(`❌ Claude CLI style failed: ${error.message}`);
    console.log(`   Error code: ${error.code}`);
    return false;
  }

  console.log('\n🎉 All connectivity tests passed!');
  return true;
}

async function testNetworkIssues() {
  console.log('\n🔧 Testing for common network issues...\n');
  
  // Test DNS resolution
  console.log('1. Testing DNS resolution...');
  const url = new URL(PROXY_URL);
  console.log(`   Host: ${url.hostname}`);
  console.log(`   Port: ${url.port || (url.protocol === 'https:' ? 443 : 80)}`);
  
  // Test if port is open
  console.log('\n2. Testing port connectivity...');
  try {
    const response = await makeRequest(`${PROXY_URL}/health`, {
      method: 'GET',
      timeout: 2000
    });
    console.log(`✅ Port is open and responding`);
  } catch (error) {
    console.log(`❌ Port connectivity issue: ${error.message}`);
    if (error.code === 'ECONNREFUSED') {
      console.log('   → Server is not running or port is blocked');
    } else if (error.code === 'ETIMEDOUT') {
      console.log('   → Connection timeout - firewall or network issue');
    } else if (error.code === 'ENOTFOUND') {
      console.log('   → DNS resolution failed');
    }
  }
}

async function main() {
  const args = process.argv.slice(2);
  
  console.log(`Testing connectivity to: ${PROXY_URL}`);
  console.log(`Using API key: ${API_KEY}\n`);
  
  if (args.includes('--network')) {
    await testNetworkIssues();
  } else {
    const success = await testBasicConnectivity();
    if (!success) {
      console.log('\n💡 Try running with --network flag to diagnose network issues');
    }
  }
}

if (require.main === module) {
  main().catch(console.error);
}
