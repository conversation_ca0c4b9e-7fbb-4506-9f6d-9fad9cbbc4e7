const express = require('express');
const axios = require('axios');
const { v4: uuidv4 } = require('uuid');
const logger = require('../utils/logger');
const RequestTranslator = require('../translators/requestTranslator');
const ResponseTranslator = require('../translators/responseTranslator');
const StreamingService = require('../services/streamingService');

/**
 * Creates the main proxy router for handling message requests
 */
function createProxyRouter(config) {
  const router = express.Router();
  const requestTranslator = new RequestTranslator(config);
  const responseTranslator = new ResponseTranslator(config);
  const streamingService = new StreamingService(config);

  /**
   * POST /v1/messages - Create a message (main chat completion endpoint)
   */
  router.post('/messages', async (req, res, next) => {
    try {
      const requestId = req.headers['x-request-id'] || `req_${uuidv4()}`;
      res.setHeader('x-request-id', requestId);

      logger.info('Received message request', { requestId });
      
      // Validate required fields with exact Anthropic error format
      if (!req.body.model) {
        return res.status(400).json({
          type: 'error',
          error: {
            type: 'invalid_request_error',
            message: 'model: Field required'
          }
        });
      }

      if (!req.body.messages || !Array.isArray(req.body.messages)) {
        return res.status(400).json({
          type: 'error',
          error: {
            type: 'invalid_request_error',
            message: 'messages: Field required'
          }
        });
      }

      if (typeof req.body.max_tokens !== 'number' || req.body.max_tokens <= 0) {
        return res.status(400).json({
          type: 'error',
          error: {
            type: 'invalid_request_error',
            message: 'max_tokens: Field required and must be a positive integer'
          }
        });
      }

      // Validate messages array
      for (let i = 0; i < req.body.messages.length; i++) {
        const message = req.body.messages[i];
        if (!message.role || !['user', 'assistant'].includes(message.role)) {
          return res.status(400).json({
            type: 'error',
            error: {
              type: 'invalid_request_error',
              message: `messages.${i}.role: Must be 'user' or 'assistant'`
            }
          });
        }
        if (!message.content) {
          return res.status(400).json({
            type: 'error',
            error: {
              type: 'invalid_request_error',
              message: `messages.${i}.content: Field required`
            }
          });
        }
      }

      // Store original model for response
      const originalModel = req.body.model;

      // Translate request from Anthropic to OpenAI format
      const openaiRequest = requestTranslator.translateMessagesRequest(req.body);

      logger.debug('Request translated successfully', { 
        originalModel, 
        translatedModel: openaiRequest.model 
      });

      // Handle streaming vs non-streaming
      if (req.body.stream) {
        await streamingService.handleStreamingRequest(openaiRequest, res, originalModel);
      } else {
        await handleNonStreamingRequest(openaiRequest, res, originalModel, config, responseTranslator);
      }

    } catch (error) {
      logger.error('Error in message request:', error.message);
      next(error);
    }
  });

  /**
   * POST /v1/messages/count_tokens - Count tokens for a message
   */
  router.post('/messages/count_tokens', async (req, res, next) => {
    try {
      logger.info('Received token count request');

      // Validate required fields
      if (!req.body.model || !req.body.messages) {
        return res.status(400).json({
          type: 'error',
          error: {
            type: 'invalid_request_error',
            message: 'Missing required fields: model and messages'
          }
        });
      }

      // For token counting, we'll make a request with max_tokens=1 and extract the prompt tokens
      const countRequest = {
        ...req.body,
        max_tokens: 1,
        stream: false
      };

      const openaiRequest = requestTranslator.translateMessagesRequest(countRequest);

      const response = await axios.post(`${config.openai.base_url}/chat/completions`, openaiRequest, {
        headers: {
          'Authorization': `Bearer ${config.openai.api_key}`,
          'Content-Type': 'application/json'
        },
        timeout: config.openai.timeout || 30000
      });

      // Return just the input token count
      res.json({
        input_tokens: response.data.usage?.prompt_tokens || 0
      });

    } catch (error) {
      logger.error('Error in token count request:', error.message);
      next(error);
    }
  });

  return router;
}

/**
 * Handles non-streaming requests
 */
async function handleNonStreamingRequest(openaiRequest, res, originalModel, config, responseTranslator) {
  try {
    const response = await axios.post(`${config.openai.base_url}/chat/completions`, openaiRequest, {
      headers: {
        'Authorization': `Bearer ${config.openai.api_key}`,
        'Content-Type': 'application/json'
      },
      timeout: config.openai.timeout || 30000
    });

    // Translate response from OpenAI to Anthropic format
    const anthropicResponse = responseTranslator.translateChatCompletionResponse(
      response.data,
      originalModel
    );

    logger.debug('Response translated successfully');
    res.json(anthropicResponse);

  } catch (error) {
    logger.error('OpenAI API request failed:', error.message);
    throw error;
  }
}

module.exports = { createProxyRouter };
