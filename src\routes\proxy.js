const express = require('express');
const axios = require('axios');
const { v4: uuidv4 } = require('uuid');
const logger = require('../utils/logger');
const RequestTranslator = require('../translators/requestTranslator');
const ResponseTranslator = require('../translators/responseTranslator');
const StreamingService = require('../services/streamingService');

/**
 * Creates the main proxy router for handling message requests
 */
function createProxyRouter(config) {
  const router = express.Router();
  const requestTranslator = new RequestTranslator(config);
  const responseTranslator = new ResponseTranslator(config);
  const streamingService = new StreamingService(config);

  /**
   * POST /v1/messages - Create a message (main chat completion endpoint)
   */
  router.post('/messages', async (req, res, next) => {
    try {
      const requestId = req.headers['x-request-id'] || `req_${uuidv4()}`;
      res.setHeader('x-request-id', requestId);

      logger.info('Received message request', { requestId });
      
      // Validate required fields with exact Anthropic error format
      if (!req.body.model) {
        return res.status(400).json({
          type: 'error',
          error: {
            type: 'invalid_request_error',
            message: 'model: Field required'
          }
        });
      }

      if (!req.body.messages || !Array.isArray(req.body.messages)) {
        return res.status(400).json({
          type: 'error',
          error: {
            type: 'invalid_request_error',
            message: 'messages: Field required'
          }
        });
      }

      if (typeof req.body.max_tokens !== 'number' || req.body.max_tokens <= 0) {
        return res.status(400).json({
          type: 'error',
          error: {
            type: 'invalid_request_error',
            message: 'max_tokens: Field required and must be a positive integer'
          }
        });
      }

      // Check if max_tokens is too large and provide helpful error
      const modelLimits = {
        'gpt-4o': 16384,
        'gpt-4o-mini': 16384,
        'gpt-4-turbo': 4096,
        'gpt-4': 4096,
        'gpt-3.5-turbo': 4096,
        'gpt-4.1': 16384,
        'gpt-4.1-mini': 16384,
        'gpt-4.1-nano': 16384
      };

      const limit = modelLimits[req.body.model] || 4096;
      if (req.body.max_tokens > limit) {
        logger.warn(`max_tokens ${req.body.max_tokens} exceeds limit ${limit} for model ${req.body.model}, will be adjusted`);
        // Don't return error, just log and let the translator adjust it
      }

      // Validate messages array
      for (let i = 0; i < req.body.messages.length; i++) {
        const message = req.body.messages[i];
        if (!message.role || !['user', 'assistant'].includes(message.role)) {
          return res.status(400).json({
            type: 'error',
            error: {
              type: 'invalid_request_error',
              message: `messages.${i}.role: Must be 'user' or 'assistant'`
            }
          });
        }
        if (!message.content) {
          return res.status(400).json({
            type: 'error',
            error: {
              type: 'invalid_request_error',
              message: `messages.${i}.content: Field required`
            }
          });
        }
      }

      // Store original model for response
      const originalModel = req.body.model;

      // Validate model exists before making request
      await validateModelExists(originalModel, config);

      // Translate request from Anthropic to OpenAI format
      const openaiRequest = requestTranslator.translateMessagesRequest(req.body);

      // Validate the translated request is valid JSON
      try {
        JSON.stringify(openaiRequest);
      } catch (jsonError) {
        logger.error('Generated invalid JSON for OpenAI request:', {
          error: jsonError.message,
          request: openaiRequest
        });
        return res.status(400).json({
          type: 'error',
          error: {
            type: 'invalid_request_error',
            message: 'Internal error: Generated invalid request format'
          }
        });
      }

      logger.debug('Request translated successfully', {
        originalModel,
        translatedModel: openaiRequest.model,
        openaiRequest: JSON.stringify(openaiRequest, null, 2)
      });

      // Handle streaming vs non-streaming
      if (req.body.stream) {
        await streamingService.handleStreamingRequest(openaiRequest, res, originalModel);
      } else {
        await handleNonStreamingRequest(openaiRequest, res, originalModel, config, responseTranslator);
      }

    } catch (error) {
      logger.error('Error in message request:', error.message);
      next(error);
    }
  });

  /**
   * POST /v1/messages/count_tokens - Count tokens for a message
   */
  router.post('/messages/count_tokens', async (req, res, next) => {
    try {
      logger.info('Received token count request');

      // Validate required fields
      if (!req.body.model || !req.body.messages) {
        return res.status(400).json({
          type: 'error',
          error: {
            type: 'invalid_request_error',
            message: 'Missing required fields: model and messages'
          }
        });
      }

      // Validate model exists
      await validateModelExists(req.body.model, config);

      // For token counting, we'll make a request with max_tokens=1 and extract the prompt tokens
      const countRequest = {
        ...req.body,
        max_tokens: 1,
        stream: false
      };

      const openaiRequest = requestTranslator.translateMessagesRequest(countRequest);

      const response = await axios.post(`${config.openai.base_url}/chat/completions`, openaiRequest, {
        headers: {
          'Authorization': `Bearer ${config.openai.api_key}`,
          'Content-Type': 'application/json'
        },
        timeout: config.openai.timeout || 30000
      });

      // Return just the input token count
      res.json({
        input_tokens: response.data.usage?.prompt_tokens || 0
      });

    } catch (error) {
      logger.error('Error in token count request:', error.message);
      next(error);
    }
  });

  return router;
}

/**
 * Validates that a model exists in OpenAI
 */
async function validateModelExists(modelName, config) {
  try {
    logger.debug(`Validating model exists: ${modelName}`);

    const response = await axios.get(`${config.openai.base_url}/models/${modelName}`, {
      headers: {
        'Authorization': `Bearer ${config.openai.api_key}`
      },
      timeout: config.openai.timeout || 30000
    });

    logger.debug(`Model ${modelName} validated successfully`);
    return true;
  } catch (error) {
    if (error.response?.status === 404) {
      logger.error(`Model '${modelName}' does not exist in OpenAI API`);

      // Throw a proper Anthropic-style error
      const anthropicError = new Error(`Model '${modelName}' not found`);
      anthropicError.status = 400;
      anthropicError.anthropicError = {
        type: 'error',
        error: {
          type: 'invalid_request_error',
          message: `The model '${modelName}' does not exist. Please check the model name and try again.`
        }
      };
      throw anthropicError;
    }

    // For other errors (network, auth, etc.), let them bubble up
    logger.error(`Error validating model ${modelName}:`, error.message);
    throw error;
  }
}

/**
 * Handles non-streaming requests
 */
async function handleNonStreamingRequest(openaiRequest, res, originalModel, config, responseTranslator) {
  try {
    // Log the exact request being sent to OpenAI
    const requestBody = JSON.stringify(openaiRequest);
    logger.info('Sending request to OpenAI:', {
      url: `${config.openai.base_url}/chat/completions`,
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${config.openai.api_key.substring(0, 10)}...`,
        'Content-Type': 'application/json'
      },
      bodyLength: requestBody.length,
      body: requestBody
    });

    const response = await axios.post(`${config.openai.base_url}/chat/completions`, openaiRequest, {
      headers: {
        'Authorization': `Bearer ${config.openai.api_key}`,
        'Content-Type': 'application/json'
      },
      timeout: config.openai.timeout || 30000,
      // Ensure proper JSON serialization
      transformRequest: [(data) => {
        const jsonString = JSON.stringify(data);
        logger.debug('Final JSON being sent:', jsonString);
        return jsonString;
      }]
    });

    logger.info('OpenAI API response received:', {
      status: response.status,
      statusText: response.statusText
    });

    // Translate response from OpenAI to Anthropic format
    const anthropicResponse = responseTranslator.translateChatCompletionResponse(
      response.data,
      originalModel
    );

    logger.debug('Response translated successfully');
    res.json(anthropicResponse);

  } catch (error) {
    logger.error('OpenAI API request failed:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      config: {
        url: error.config?.url,
        method: error.config?.method,
        headers: error.config?.headers,
        data: error.config?.data
      }
    });
    throw error;
  }
}

module.exports = { createProxyRouter };
