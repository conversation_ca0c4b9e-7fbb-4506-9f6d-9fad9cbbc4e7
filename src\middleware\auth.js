const logger = require('../utils/logger');

/**
 * Authentication middleware that validates Anthropic-style API keys
 */
const authenticateRequest = (config) => {
  return (req, res, next) => {
    // Extract API key from headers (support multiple formats for Claude CLI compatibility)
    const apiKey = req.headers['x-api-key'] ||
                   req.headers['authorization']?.replace('Bearer ', '') ||
                   req.headers['anthropic-api-key'];

    if (!apiKey) {
      logger.warn('Request missing API key', {
        url: req.url,
        method: req.method,
        headers: Object.keys(req.headers) // Log header names only for security
      });

      return res.status(401).json({
        type: 'error',
        error: {
          type: 'authentication_error',
          message: 'Missing API key. Please provide your API key in the x-api-key header.'
        }
      });
    }

    // For this proxy, we'll accept any API key since we're using OpenAI's key for the backend
    // In a production environment, you might want to validate against a list of allowed keys
    if (config.auth?.validate_keys && !config.auth.allowed_keys.includes(apiKey)) {
      logger.warn('Invalid API key provided', {
        url: req.url,
        method: req.method,
        apiKey: apiKey.substring(0, 10) + '...' // Log only first 10 chars for security
      });

      return res.status(401).json({
        type: 'error',
        error: {
          type: 'authentication_error',
          message: 'Invalid API key provided.'
        }
      });
    }

    // Store the API key in the request for potential future use
    req.apiKey = apiKey;
    
    logger.debug('Request authenticated successfully');
    next();
  };
};

/**
 * Middleware to validate Anthropic API version header
 */
const validateApiVersion = (req, res, next) => {
  const apiVersion = req.headers['anthropic-version'];
  
  // Anthropic typically requires this header, but we'll make it optional for flexibility
  if (apiVersion && !['2023-06-01', '2023-01-01'].includes(apiVersion)) {
    logger.warn('Unsupported API version', { apiVersion });
    
    return res.status(400).json({
      type: 'error',
      error: {
        type: 'invalid_request_error',
        message: `Unsupported API version: ${apiVersion}. Supported versions: 2023-06-01, 2023-01-01`
      }
    });
  }

  next();
};

/**
 * Middleware to validate content type for POST requests
 */
const validateContentType = (req, res, next) => {
  if (req.method === 'POST' && !req.headers['content-type']?.includes('application/json')) {
    return res.status(400).json({
      type: 'error',
      error: {
        type: 'invalid_request_error',
        message: 'Content-Type must be application/json'
      }
    });
  }

  next();
};

/**
 * Middleware to add security headers
 */
const addSecurityHeaders = (req, res, next) => {
  // Add security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  next();
};

module.exports = {
  authenticateRequest,
  validateApiVersion,
  validateContentType,
  addSecurityHeaders
};
