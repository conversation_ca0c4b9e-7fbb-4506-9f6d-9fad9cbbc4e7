#!/usr/bin/env node

/**
 * Setup script for OpenAI-Anthropic Proxy
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function setup() {
  console.log('🚀 OpenAI-Anthropic Proxy Setup\n');
  
  try {
    // Check if config.json exists
    const configPath = path.join(__dirname, 'config.json');
    let config;
    
    if (fs.existsSync(configPath)) {
      console.log('📁 Found existing config.json');
      const overwrite = await question('Do you want to update the configuration? (y/N): ');
      
      if (overwrite.toLowerCase() !== 'y') {
        console.log('Setup cancelled.');
        rl.close();
        return;
      }
      
      config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
    } else {
      // Load template config
      config = {
        "openai": {
          "api_key": "YOUR_OPENAI_API_KEY_HERE",
          "base_url": "https://api.openai.com/v1",
          "timeout": 30000,
          "max_retries": 3
        },
        "server": {
          "host": "localhost",
          "port": 3000,
          "cors": {
            "enabled": true,
            "origins": ["*"]
          }
        },
        "logging": {
          "level": "info",
          "enable_request_logging": true,
          "enable_response_logging": false
        },
        "rate_limiting": {
          "enabled": false,
          "requests_per_minute": 60,
          "burst_limit": 10
        },
        "model_mapping": {
          "claude-3-5-sonnet-20241022": "gpt-4o",
          "claude-3-5-haiku-20241022": "gpt-4o-mini",
          "claude-3-opus-20240229": "gpt-4-turbo",
          "claude-3-sonnet-20240229": "gpt-4",
          "claude-3-haiku-20240307": "gpt-3.5-turbo"
        },
        "features": {
          "streaming": true,
          "tool_calling": true,
          "vision": true,
          "token_counting": true
        },
        "auth": {
          "validate_keys": false,
          "allowed_keys": []
        }
      };
    }

    // Get OpenAI API key
    console.log('\n🔑 OpenAI Configuration');
    const apiKey = await question(`Enter your OpenAI API key [${config.openai.api_key}]: `);
    if (apiKey.trim()) {
      config.openai.api_key = apiKey.trim();
    }

    // Get base URL
    const baseUrl = await question(`Enter OpenAI base URL [${config.openai.base_url}]: `);
    if (baseUrl.trim()) {
      config.openai.base_url = baseUrl.trim();
    }

    // Server configuration
    console.log('\n🌐 Server Configuration');
    const host = await question(`Enter server host [${config.server.host}]: `);
    if (host.trim()) {
      config.server.host = host.trim();
    }

    const port = await question(`Enter server port [${config.server.port}]: `);
    if (port.trim()) {
      config.server.port = parseInt(port.trim());
    }

    // Logging configuration
    console.log('\n📝 Logging Configuration');
    const logLevel = await question(`Enter log level (debug/info/warn/error) [${config.logging.level}]: `);
    if (logLevel.trim() && ['debug', 'info', 'warn', 'error'].includes(logLevel.trim())) {
      config.logging.level = logLevel.trim();
    }

    // Rate limiting
    console.log('\n🚦 Rate Limiting');
    const enableRateLimit = await question(`Enable rate limiting? (y/N) [${config.rate_limiting.enabled ? 'y' : 'N'}]: `);
    if (enableRateLimit.toLowerCase() === 'y') {
      config.rate_limiting.enabled = true;
      
      const rpmLimit = await question(`Requests per minute [${config.rate_limiting.requests_per_minute}]: `);
      if (rpmLimit.trim()) {
        config.rate_limiting.requests_per_minute = parseInt(rpmLimit.trim());
      }
    } else {
      config.rate_limiting.enabled = false;
    }

    // Validate API key
    if (!config.openai.api_key || config.openai.api_key === 'YOUR_OPENAI_API_KEY_HERE') {
      console.log('\n⚠️  Warning: No valid OpenAI API key provided. Please update config.json before starting the server.');
    }

    // Save configuration
    fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
    console.log('\n✅ Configuration saved to config.json');

    // Show next steps
    console.log('\n🎉 Setup complete! Next steps:');
    console.log('1. Install dependencies: npm install');
    console.log('2. Start the server: npm start');
    console.log('3. Test the proxy: npm run test:proxy');
    console.log('\n📖 See README.md for usage examples and API documentation.');

  } catch (error) {
    console.error('❌ Setup failed:', error.message);
  } finally {
    rl.close();
  }
}

// Run setup if this script is executed directly
if (require.main === module) {
  setup();
}

module.exports = { setup };
