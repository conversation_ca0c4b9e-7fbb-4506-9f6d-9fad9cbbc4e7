#!/usr/bin/env node

/**
 * Check if o3 model exists in OpenAI API
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Load config
const configPath = path.join(__dirname, 'config.json');
const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));

async function checkO3Model() {
  console.log('🔍 Checking if o3 model exists in OpenAI API...\n');
  
  try {
    const response = await axios.get(`${config.openai.base_url}/models/o3`, {
      headers: {
        'Authorization': `Bearer ${config.openai.api_key}`
      },
      timeout: 30000
    });

    console.log('✅ o3 model EXISTS in your OpenAI account');
    console.log('Model details:', JSON.stringify(response.data, null, 2));
    return true;
  } catch (error) {
    if (error.response?.status === 404) {
      console.log('❌ o3 model does NOT exist in your OpenAI account');
      console.log('Available models with "o3" in the name:');
      
      // List all models and filter for o3-related ones
      try {
        const modelsResponse = await axios.get(`${config.openai.base_url}/models`, {
          headers: {
            'Authorization': `Bearer ${config.openai.api_key}`
          }
        });
        
        const o3Models = modelsResponse.data.data.filter(model => 
          model.id.toLowerCase().includes('o3')
        );
        
        if (o3Models.length > 0) {
          o3Models.forEach(model => {
            console.log(`  - ${model.id}`);
          });
          console.log('\n💡 Try using one of these models instead of "o3"');
        } else {
          console.log('  No models with "o3" in the name found');
        }
      } catch (listError) {
        console.log('  Could not list models:', listError.message);
      }
      
      return false;
    } else {
      console.log('❌ Error checking o3 model:', error.message);
      return false;
    }
  }
}

async function testProxyWithO3() {
  console.log('\n🔍 Testing proxy with o3 model...\n');
  
  try {
    const response = await axios.post('http://localhost:4141/v1/messages', {
      model: 'o3',
      max_tokens: 10,
      messages: [
        {
          role: 'user',
          content: 'Say hello'
        }
      ]
    }, {
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': 'dummy',
        'anthropic-version': '2023-06-01'
      },
      timeout: 30000
    });

    console.log('✅ Proxy accepted o3 model');
    console.log('Response model:', response.data.model);
    console.log('Response:', response.data.content[0].text);
    return true;
  } catch (error) {
    console.log('❌ Proxy rejected o3 model');
    console.log('Status:', error.response?.status);
    console.log('Error:', JSON.stringify(error.response?.data, null, 2));
    return false;
  }
}

async function main() {
  const modelExists = await checkO3Model();
  
  if (modelExists) {
    await testProxyWithO3();
  } else {
    console.log('\n🔧 The o3 model does not exist in your OpenAI account.');
    console.log('This is why the proxy might be falling back to other models.');
    console.log('\nTo fix this, either:');
    console.log('1. Use a model that exists (like o3-pro or o3-pro-2025-06-10)');
    console.log('2. Wait for o3 to be available in your account');
  }
}

if (require.main === module) {
  main().catch(console.error);
}
