const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const fs = require('fs');
const path = require('path');
const logger = require('./utils/logger');
const { createProxyRouter } = require('./routes/proxy');
const { createModelsRouter } = require('./routes/models');
const { errorHandler } = require('./middleware/errorHandler');
const {
  authenticateRequest,
  validateApiVersion,
  validateContentType,
  addSecurityHeaders
} = require('./middleware/auth');

class ProxyServer {
  constructor() {
    this.app = express();
    this.config = this.loadConfig();
    this.setupMiddleware();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  loadConfig() {
    try {
      const configPath = path.join(__dirname, '..', 'config.json');
      const configData = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(configData);
      
      // Validate required configuration
      if (!config.openai?.api_key || config.openai.api_key === 'YOUR_OPENAI_API_KEY_HERE') {
        throw new Error('OpenAI API key not configured. Please update config.json');
      }
      
      return config;
    } catch (error) {
      logger.error('Failed to load configuration:', error.message);
      process.exit(1);
    }
  }

  setupMiddleware() {
    // Security middleware
    this.app.use(helmet());
    
    // CORS configuration
    if (this.config.server.cors.enabled) {
      this.app.use(cors({
        origin: this.config.server.cors.origins,
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'x-api-key', 'anthropic-version', 'anthropic-api-key'],
        exposedHeaders: ['Content-Type', 'x-request-id']
      }));
    }

    // Rate limiting
    if (this.config.rate_limiting.enabled) {
      const limiter = rateLimit({
        windowMs: 60 * 1000, // 1 minute
        max: this.config.rate_limiting.requests_per_minute,
        standardHeaders: true,
        legacyHeaders: false,
        message: {
          error: {
            type: 'rate_limit_error',
            message: 'Too many requests, please try again later.'
          }
        }
      });
      this.app.use(limiter);
    }

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true }));

    // Request logging
    if (this.config.logging.enable_request_logging) {
      this.app.use((req, res, next) => {
        logger.info(`${req.method} ${req.path}`, {
          headers: req.headers,
          body: req.body
        });
        next();
      });
    }
  }

  setupRoutes() {
    // Health check
    this.app.get('/health', (req, res) => {
      res.json({ status: 'healthy', timestamp: new Date().toISOString() });
    });

    // Add security headers to all routes
    this.app.use(addSecurityHeaders);

    // API routes with authentication and validation
    this.app.use('/v1',
      authenticateRequest(this.config),
      validateApiVersion,
      validateContentType,
      createProxyRouter(this.config)
    );

    this.app.use('/v1/models',
      authenticateRequest(this.config),
      validateApiVersion,
      createModelsRouter(this.config)
    );

    // 404 handler
    this.app.use('*', (req, res) => {
      res.status(404).json({
        error: {
          type: 'not_found_error',
          message: `Endpoint ${req.method} ${req.originalUrl} not found`
        }
      });
    });
  }

  setupErrorHandling() {
    this.app.use(errorHandler);
  }

  start() {
    const { host, port } = this.config.server;
    
    this.app.listen(port, host, () => {
      logger.info(`OpenAI-Anthropic Proxy Server started on ${host}:${port}`);
      logger.info('Configuration loaded:', {
        openai_base_url: this.config.openai.base_url,
        features: this.config.features,
        model_mapping_enabled: this.config.model_mapping?.enabled || false
      });
    });
  }
}

// Start the server
if (require.main === module) {
  const server = new ProxyServer();
  server.start();
}

module.exports = ProxyServer;
