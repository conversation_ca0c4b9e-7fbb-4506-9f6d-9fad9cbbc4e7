const logger = require('../utils/logger');

/**
 * Translates Anthropic API requests to OpenAI API format
 */
class RequestTranslator {
  constructor(config) {
    this.config = config;
    this.modelMapping = config.model_mapping || {};
  }

  /**
   * Main translation method for /v1/messages requests
   */
  translateMessagesRequest(anthropicRequest) {
    const model = this.translateModel(anthropicRequest.model);
    const maxTokens = this.adjustMaxTokens(anthropicRequest.max_tokens, model);

    const openaiRequest = {
      model: model,
      messages: this.translateMessages(anthropicRequest.messages, anthropicRequest.system),
      max_tokens: maxTokens,
      stream: anthropicRequest.stream || false
    };

    // Optional parameters
    if (anthropicRequest.temperature !== undefined) {
      openaiRequest.temperature = Math.min(anthropicRequest.temperature, 2.0);
    }

    if (anthropicRequest.top_p !== undefined) {
      openaiRequest.top_p = anthropicRequest.top_p;
    }

    if (anthropicRequest.stop_sequences) {
      openaiRequest.stop = anthropicRequest.stop_sequences;
    }

    if (anthropicRequest.metadata?.user_id) {
      openaiRequest.user = anthropicRequest.metadata.user_id;
    }

    // Tool handling
    if (anthropicRequest.tools) {
      openaiRequest.tools = this.translateTools(anthropicRequest.tools);
      
      if (anthropicRequest.tool_choice) {
        openaiRequest.tool_choice = this.translateToolChoice(anthropicRequest.tool_choice);
      }
    }

    logger.debug('Translated request:', { anthropicRequest, openaiRequest });
    return openaiRequest;
  }

  /**
   * Translates model name - now passes through OpenAI models directly
   */
  translateModel(modelName) {
    // If model mapping is disabled, pass through the model name directly
    if (!this.config.model_mapping?.enabled) {
      logger.debug(`Model mapping disabled, using model as-is: ${modelName}`);
      return modelName;
    }

    // Legacy mapping support (if enabled)
    const mappedModel = this.modelMapping[modelName];
    if (!mappedModel) {
      logger.warn(`No mapping found for model: ${modelName}, using as-is`);
      return modelName;
    }
    return mappedModel;
  }

  /**
   * Adjust max_tokens to fit within model limits
   */
  adjustMaxTokens(requestedTokens, model) {
    // OpenAI model token limits (completion tokens)
    const modelLimits = {
      'gpt-4o': 16384,
      'gpt-4o-mini': 16384,
      'gpt-4-turbo': 4096,
      'gpt-4': 4096,
      'gpt-3.5-turbo': 4096,
      'gpt-4.1': 16384,
      'gpt-4.1-mini': 16384,
      'gpt-4.1-nano': 16384
    };

    const limit = modelLimits[model] || 4096; // Default to 4096 if model not found

    if (requestedTokens > limit) {
      logger.warn(`Adjusting max_tokens from ${requestedTokens} to ${limit} for model ${model}`);
      return limit;
    }

    return requestedTokens;
  }

  /**
   * Translates messages array from Anthropic to OpenAI format
   */
  translateMessages(anthropicMessages, systemPrompt) {
    const systemMessages = this.handleSystemPrompt(systemPrompt);

    const otherMessages = anthropicMessages.flatMap((message) =>
      message.role === 'user' ?
        this.handleUserMessage(message) :
        this.handleAssistantMessage(message)
    );

    return [...systemMessages, ...otherMessages];
  }

  /**
   * Handle system prompt conversion
   */
  handleSystemPrompt(system) {
    if (!system) {
      return [];
    }

    if (typeof system === 'string') {
      return [{ role: 'system', content: system }];
    } else if (Array.isArray(system)) {
      const systemText = system.map(block => block.text).join('\n\n');
      return [{ role: 'system', content: systemText }];
    }

    return [];
  }

  /**
   * Handle user message conversion with tool results
   */
  handleUserMessage(message) {
    const newMessages = [];

    if (Array.isArray(message.content)) {
      const toolResultBlocks = message.content.filter(block => block.type === 'tool_result');
      const otherBlocks = message.content.filter(block => block.type !== 'tool_result');

      if (otherBlocks.length > 0) {
        newMessages.push({
          role: 'user',
          content: this.mapContent(otherBlocks)
        });
      }

      for (const block of toolResultBlocks) {
        newMessages.push({
          role: 'tool',
          tool_call_id: block.tool_use_id,
          content: block.content
        });
      }
    } else {
      newMessages.push({
        role: 'user',
        content: this.mapContent(message.content)
      });
    }

    return newMessages;
  }

  /**
   * Handle assistant message conversion
   */
  handleAssistantMessage(message) {
    if (!Array.isArray(message.content)) {
      return [{
        role: 'assistant',
        content: this.mapContent(message.content)
      }];
    }

    const toolUseBlocks = message.content.filter(block => block.type === 'tool_use');
    const textBlocks = message.content.filter(block => block.type === 'text');

    if (toolUseBlocks.length > 0) {
      return [{
        role: 'assistant',
        content: textBlocks.map(b => b.text).join('\n\n') || null,
        tool_calls: toolUseBlocks.map(toolUse => ({
          id: toolUse.id,
          type: 'function',
          function: {
            name: toolUse.name,
            arguments: JSON.stringify(toolUse.input)
          }
        }))
      }];
    } else {
      return [{
        role: 'assistant',
        content: this.mapContent(message.content)
      }];
    }
  }



  /**
   * Maps content from Anthropic to OpenAI format
   */
  mapContent(content) {
    if (typeof content === 'string') {
      return content;
    }
    if (!Array.isArray(content)) {
      return null;
    }

    const hasImage = content.some(block => block.type === 'image');
    if (!hasImage) {
      return content
        .filter(block => block.type === 'text')
        .map(block => block.text)
        .join('\n\n');
    }

    const contentParts = [];
    for (const block of content) {
      if (block.type === 'text') {
        contentParts.push({ type: 'text', text: block.text });
      } else if (block.type === 'image') {
        contentParts.push({
          type: 'image_url',
          image_url: {
            url: `data:${block.source.media_type};base64,${block.source.data}`
          }
        });
      }
    }
    return contentParts;
  }

  /**
   * Translates image blocks from Anthropic to OpenAI format
   */
  translateImageBlock(anthropicImageBlock) {
    const { source } = anthropicImageBlock;
    
    if (source.type === 'base64') {
      return {
        type: 'image_url',
        image_url: {
          url: `data:${source.media_type};base64,${source.data}`,
          detail: 'auto'
        }
      };
    }

    throw new Error(`Unsupported image source type: ${source.type}`);
  }

  /**
   * Translates tools from Anthropic to OpenAI format
   */
  translateTools(anthropicTools) {
    return anthropicTools.map(tool => ({
      type: 'function',
      function: {
        name: tool.name,
        description: tool.description,
        parameters: tool.input_schema
      }
    }));
  }

  /**
   * Translates tool choice from Anthropic to OpenAI format
   */
  translateToolChoice(anthropicToolChoice) {
    if (!anthropicToolChoice) {
      return 'auto';
    }

    if (anthropicToolChoice.type === 'auto') {
      return 'auto';
    } else if (anthropicToolChoice.type === 'any') {
      return 'required';
    } else if (anthropicToolChoice.type === 'tool' && anthropicToolChoice.name) {
      return {
        type: 'function',
        function: {
          name: anthropicToolChoice.name
        }
      };
    } else if (anthropicToolChoice.type === 'none') {
      return 'none';
    }

    return 'auto';
  }
}

module.exports = RequestTranslator;
