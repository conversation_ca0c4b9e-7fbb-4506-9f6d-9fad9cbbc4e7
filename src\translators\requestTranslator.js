const logger = require('../utils/logger');

/**
 * Translates Anthropic API requests to OpenAI API format
 */
class RequestTranslator {
  constructor(config) {
    this.config = config;
    this.modelMapping = config.model_mapping || {};
  }

  /**
   * Main translation method for /v1/messages requests
   */
  translateMessagesRequest(anthropicRequest) {
    const openaiRequest = {
      model: this.translateModel(anthropicRequest.model),
      messages: this.translateMessages(anthropicRequest.messages, anthropicRequest.system),
      max_tokens: anthropicRequest.max_tokens,
      stream: anthropicRequest.stream || false
    };

    // Optional parameters
    if (anthropicRequest.temperature !== undefined) {
      openaiRequest.temperature = Math.min(anthropicRequest.temperature, 2.0);
    }

    if (anthropicRequest.top_p !== undefined) {
      openaiRequest.top_p = anthropicRequest.top_p;
    }

    if (anthropicRequest.stop_sequences) {
      openaiRequest.stop = anthropicRequest.stop_sequences;
    }

    if (anthropicRequest.metadata?.user_id) {
      openaiRequest.user = anthropicRequest.metadata.user_id;
    }

    // Tool handling
    if (anthropicRequest.tools) {
      openaiRequest.tools = this.translateTools(anthropicRequest.tools);
      
      if (anthropicRequest.tool_choice) {
        openaiRequest.tool_choice = this.translateToolChoice(anthropicRequest.tool_choice);
      }
    }

    logger.debug('Translated request:', { anthropicRequest, openaiRequest });
    return openaiRequest;
  }

  /**
   * Translates model name using the mapping configuration
   */
  translateModel(anthropicModel) {
    const mappedModel = this.modelMapping[anthropicModel];
    if (!mappedModel) {
      logger.warn(`No mapping found for model: ${anthropicModel}, using as-is`);
      return anthropicModel;
    }
    return mappedModel;
  }

  /**
   * Translates messages array from Anthropic to OpenAI format
   */
  translateMessages(anthropicMessages, systemPrompt) {
    const openaiMessages = [];

    // Add system message if provided
    if (systemPrompt) {
      openaiMessages.push({
        role: 'system',
        content: typeof systemPrompt === 'string' ? systemPrompt : systemPrompt.text
      });
    }

    // Translate each message
    for (const message of anthropicMessages) {
      if (message.role === 'user') {
        openaiMessages.push(this.translateUserMessage(message));
      } else if (message.role === 'assistant') {
        openaiMessages.push(this.translateAssistantMessage(message));
      }
    }

    return openaiMessages;
  }

  /**
   * Translates a user message from Anthropic to OpenAI format
   */
  translateUserMessage(anthropicMessage) {
    const openaiMessage = {
      role: 'user',
      content: this.translateContent(anthropicMessage.content)
    };

    return openaiMessage;
  }

  /**
   * Translates an assistant message from Anthropic to OpenAI format
   */
  translateAssistantMessage(anthropicMessage) {
    const openaiMessage = {
      role: 'assistant'
    };

    if (typeof anthropicMessage.content === 'string') {
      openaiMessage.content = anthropicMessage.content;
    } else if (Array.isArray(anthropicMessage.content)) {
      // Handle content blocks
      const textBlocks = anthropicMessage.content.filter(block => block.type === 'text');
      const toolBlocks = anthropicMessage.content.filter(block => block.type === 'tool_use');

      if (textBlocks.length > 0) {
        openaiMessage.content = textBlocks.map(block => block.text).join('\n');
      }

      if (toolBlocks.length > 0) {
        openaiMessage.tool_calls = toolBlocks.map(block => ({
          id: block.id,
          type: 'function',
          function: {
            name: block.name,
            arguments: JSON.stringify(block.input)
          }
        }));
      }
    }

    return openaiMessage;
  }

  /**
   * Translates content from Anthropic to OpenAI format
   */
  translateContent(anthropicContent) {
    if (typeof anthropicContent === 'string') {
      return anthropicContent;
    }

    if (Array.isArray(anthropicContent)) {
      const openaiContent = [];

      for (const block of anthropicContent) {
        if (block.type === 'text') {
          openaiContent.push({
            type: 'text',
            text: block.text
          });
        } else if (block.type === 'image') {
          openaiContent.push(this.translateImageBlock(block));
        } else if (block.type === 'tool_result') {
          // Tool results need to be handled as separate messages in OpenAI
          // This will be handled in the message translation logic
          continue;
        }
      }

      return openaiContent.length === 1 && openaiContent[0].type === 'text' 
        ? openaiContent[0].text 
        : openaiContent;
    }

    return anthropicContent;
  }

  /**
   * Translates image blocks from Anthropic to OpenAI format
   */
  translateImageBlock(anthropicImageBlock) {
    const { source } = anthropicImageBlock;
    
    if (source.type === 'base64') {
      return {
        type: 'image_url',
        image_url: {
          url: `data:${source.media_type};base64,${source.data}`,
          detail: 'auto'
        }
      };
    }

    throw new Error(`Unsupported image source type: ${source.type}`);
  }

  /**
   * Translates tools from Anthropic to OpenAI format
   */
  translateTools(anthropicTools) {
    return anthropicTools.map(tool => ({
      type: 'function',
      function: {
        name: tool.name,
        description: tool.description,
        parameters: tool.input_schema
      }
    }));
  }

  /**
   * Translates tool choice from Anthropic to OpenAI format
   */
  translateToolChoice(anthropicToolChoice) {
    if (typeof anthropicToolChoice === 'string') {
      return anthropicToolChoice;
    }

    if (anthropicToolChoice.type === 'auto') {
      return 'auto';
    } else if (anthropicToolChoice.type === 'any') {
      return 'required';
    } else if (anthropicToolChoice.type === 'tool') {
      return {
        type: 'function',
        function: {
          name: anthropicToolChoice.name
        }
      };
    }

    return 'auto';
  }
}

module.exports = RequestTranslator;
