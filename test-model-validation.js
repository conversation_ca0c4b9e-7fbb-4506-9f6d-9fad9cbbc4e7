#!/usr/bin/env node

/**
 * Test script to verify model validation works correctly
 */

const axios = require('axios');

const PROXY_URL = process.env.ANTHROPIC_BASE_URL || 'http://localhost:4141';
const API_KEY = process.env.ANTHROPIC_AUTH_TOKEN || 'dummy';

async function testValidModel() {
  console.log('🔍 Testing with valid model (gpt-4o)...');
  try {
    const response = await axios.post(`${PROXY_URL}/v1/messages`, {
      model: 'gpt-4o',
      max_tokens: 10,
      messages: [
        {
          role: 'user',
          content: 'Hi'
        }
      ]
    }, {
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01'
      },
      timeout: 30000
    });

    console.log('✅ Valid model test passed');
    console.log(`   Response: ${response.data.content[0].text.substring(0, 50)}...`);
    return true;
  } catch (error) {
    console.log('❌ Valid model test failed');
    console.log('   Error:', error.response?.data || error.message);
    return false;
  }
}

async function testInvalidModel() {
  console.log('\n🔍 Testing with invalid model (nonexistent-model)...');
  try {
    const response = await axios.post(`${PROXY_URL}/v1/messages`, {
      model: 'nonexistent-model',
      max_tokens: 10,
      messages: [
        {
          role: 'user',
          content: 'Hi'
        }
      ]
    }, {
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01'
      },
      timeout: 30000
    });

    console.log('❌ Invalid model test failed - should have thrown error');
    console.log('   Unexpected response:', response.data);
    return false;
  } catch (error) {
    if (error.response?.status === 400) {
      console.log('✅ Invalid model test passed - correctly threw error');
      console.log('   Error response:', JSON.stringify(error.response.data, null, 2));
      return true;
    } else {
      console.log('❌ Invalid model test failed - wrong error type');
      console.log('   Error:', error.response?.data || error.message);
      return false;
    }
  }
}

async function testO3Model() {
  console.log('\n🔍 Testing with o3 model...');
  try {
    const response = await axios.post(`${PROXY_URL}/v1/messages`, {
      model: 'o3',
      max_tokens: 10,
      messages: [
        {
          role: 'user',
          content: 'Hi'
        }
      ]
    }, {
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01'
      },
      timeout: 30000
    });

    console.log('✅ o3 model test passed');
    console.log(`   Response: ${response.data.content[0].text.substring(0, 50)}...`);
    return true;
  } catch (error) {
    if (error.response?.status === 400 && error.response.data?.error?.message?.includes('not found')) {
      console.log('✅ o3 model test passed - correctly threw "model not found" error');
      console.log('   Error response:', JSON.stringify(error.response.data, null, 2));
      return true;
    } else {
      console.log('❌ o3 model test failed');
      console.log('   Error:', error.response?.data || error.message);
      return false;
    }
  }
}

async function testO3ProModel() {
  console.log('\n🔍 Testing with o3-pro model...');
  try {
    const response = await axios.post(`${PROXY_URL}/v1/messages`, {
      model: 'o3-pro',
      max_tokens: 10,
      messages: [
        {
          role: 'user',
          content: 'Hi'
        }
      ]
    }, {
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01'
      },
      timeout: 30000
    });

    console.log('✅ o3-pro model test passed');
    console.log(`   Response: ${response.data.content[0].text.substring(0, 50)}...`);
    return true;
  } catch (error) {
    if (error.response?.status === 400 && error.response.data?.error?.message?.includes('not found')) {
      console.log('⚠️  o3-pro model not available in your account');
      console.log('   Error response:', JSON.stringify(error.response.data, null, 2));
      return true; // This is expected if you don't have access
    } else {
      console.log('❌ o3-pro model test failed');
      console.log('   Error:', error.response?.data || error.message);
      return false;
    }
  }
}

async function main() {
  console.log('🧪 Testing model validation...\n');
  
  const results = await Promise.all([
    testValidModel(),
    testInvalidModel(),
    testO3Model(),
    testO3ProModel()
  ]);

  const passed = results.filter(r => r).length;
  const total = results.length;

  console.log(`\n📊 Test Results: ${passed}/${total} tests passed`);
  
  if (passed === total) {
    console.log('🎉 All model validation tests passed!');
    console.log('\n✅ The proxy now correctly:');
    console.log('   - Validates models exist before making requests');
    console.log('   - Throws proper errors for non-existent models');
    console.log('   - Does not fall back to other models');
  } else {
    console.log('⚠️  Some tests failed. Check the proxy configuration.');
  }
}

if (require.main === module) {
  main().catch(console.error);
}
