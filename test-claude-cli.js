#!/usr/bin/env node

/**
 * Test script specifically for Claude CLI compatibility
 */

const axios = require('axios');

const PROXY_URL = process.env.ANTHROPIC_BASE_URL || 'http://localhost:3000';
const API_KEY = process.env.ANTHROPIC_AUTH_TOKEN || 'dummy';
const MODEL = process.env.ANTHROPIC_MODEL || 'gpt-4.1';

async function testClaudeCLICompatibility() {
  console.log('🔍 Testing Claude CLI compatibility...\n');
  console.log(`Base URL: ${PROXY_URL}`);
  console.log(`API Key: ${API_KEY}`);
  console.log(`Model: ${MODEL}\n`);

  // Test 1: Health check
  console.log('1. Testing health endpoint...');
  try {
    const response = await axios.get(`${PROXY_URL}/health`, { timeout: 5000 });
    console.log('✅ Health check passed');
  } catch (error) {
    console.log('❌ Health check failed:', error.message);
    return false;
  }

  // Test 2: Test with exact Anthropic API format
  console.log('\n2. Testing with exact Anthropic API format...');
  try {
    const response = await axios.post(`${PROXY_URL}/v1/messages`, {
      model: MODEL,
      max_tokens: 50,
      messages: [
        {
          role: 'user',
          content: 'Say "Hello from Claude CLI test" and nothing else.'
        }
      ]
    }, {
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01',
        'User-Agent': 'claude-cli/test'
      },
      timeout: 30000
    });

    console.log('✅ Anthropic API format request successful');

    // Validate response structure matches Anthropic exactly
    const data = response.data;
    console.log('   Response structure validation:');
    console.log(`     ✅ id: ${data.id}`);
    console.log(`     ✅ type: ${data.type}`);
    console.log(`     ✅ role: ${data.role}`);
    console.log(`     ✅ content is array: ${Array.isArray(data.content)}`);
    console.log(`     ✅ model: ${data.model}`);
    console.log(`     ✅ stop_reason: ${data.stop_reason}`);
    console.log(`     ✅ usage: ${JSON.stringify(data.usage)}`);

    if (data.content && data.content[0] && data.content[0].type === 'text') {
      console.log('   Response text:', data.content[0].text);
    }

  } catch (error) {
    console.log('❌ Anthropic API format request failed');
    if (error.response) {
      console.log('   Status:', error.response.status);
      console.log('   Error:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.log('   Error:', error.message);
    }
    return false;
  }

  // Test 3: Test streaming (like Claude CLI might use)
  console.log('\n3. Testing streaming compatibility...');
  try {
    const response = await axios.post(`${PROXY_URL}/v1/messages`, {
      model: MODEL,
      max_tokens: 30,
      stream: true,
      messages: [
        {
          role: 'user',
          content: 'Count to 3'
        }
      ]
    }, {
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01',
        'Accept': 'text/event-stream'
      },
      responseType: 'stream',
      timeout: 30000
    });

    console.log('✅ Streaming request initiated');
    
    let eventCount = 0;
    response.data.on('data', (chunk) => {
      const lines = chunk.toString().split('\n');
      for (const line of lines) {
        if (line.startsWith('event:')) {
          eventCount++;
        }
      }
    });

    await new Promise((resolve) => {
      response.data.on('end', () => {
        console.log(`   Received ${eventCount} events`);
        resolve();
      });
    });

  } catch (error) {
    console.log('❌ Streaming test failed');
    console.log('   Error:', error.message);
    return false;
  }

  // Test 4: Test tool calling format
  console.log('\n4. Testing tool calling compatibility...');
  try {
    const response = await axios.post(`${PROXY_URL}/v1/messages`, {
      model: MODEL,
      max_tokens: 100,
      tools: [
        {
          name: 'get_time',
          description: 'Get the current time',
          input_schema: {
            type: 'object',
            properties: {},
            required: []
          }
        }
      ],
      tool_choice: { type: 'auto' },
      messages: [
        {
          role: 'user',
          content: 'What time is it? Use the get_time tool.'
        }
      ]
    }, {
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01'
      },
      timeout: 30000
    });

    console.log('✅ Tool calling request successful');
    console.log('   Content blocks:', response.data.content.length);

    for (let i = 0; i < response.data.content.length; i++) {
      const block = response.data.content[i];
      console.log(`   Block ${i}: type=${block.type}`);
      if (block.type === 'tool_use') {
        console.log(`     Tool: ${block.name}, ID: ${block.id}`);
      }
    }

  } catch (error) {
    console.log('❌ Tool calling test failed');
    if (error.response) {
      console.log('   Status:', error.response.status);
      console.log('   Error:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.log('   Error:', error.message);
    }
    // Don't return false here as tool calling might not be supported by all models
  }

  console.log('\n🎉 All Claude CLI compatibility tests passed!');
  console.log('\n📋 Try this command:');
  console.log(`export ANTHROPIC_BASE_URL=${PROXY_URL} ANTHROPIC_AUTH_TOKEN=${API_KEY} ANTHROPIC_MODEL=${MODEL} ANTHROPIC_SMALL_FAST_MODEL=gpt-4.1-mini && claude`);

  return true;
}

async function debugConnection() {
  console.log('🔧 Debug mode - checking connection details...\n');

  try {
    // Test basic connectivity
    const response = await axios.get(`${PROXY_URL}/health`, {
      timeout: 5000,
      validateStatus: () => true // Accept any status code
    });

    console.log('Connection details:');
    console.log('  Status:', response.status);
    console.log('  Headers:', JSON.stringify(response.headers, null, 2));
    console.log('  Data:', JSON.stringify(response.data, null, 2));

  } catch (error) {
    console.log('Connection error details:');
    console.log('  Code:', error.code);
    console.log('  Message:', error.message);
    console.log('  Stack:', error.stack);

    if (error.response) {
      console.log('  Response status:', error.response.status);
      console.log('  Response headers:', error.response.headers);
      console.log('  Response data:', error.response.data);
    }
  }

  // Test what Claude CLI might be doing
  console.log('\n🔍 Testing potential Claude CLI request patterns...\n');

  const testCases = [
    {
      name: 'Standard Anthropic format',
      url: `${PROXY_URL}/v1/messages`,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01'
      }
    },
    {
      name: 'Authorization Bearer format',
      url: `${PROXY_URL}/v1/messages`,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${API_KEY}`,
        'anthropic-version': '2023-06-01'
      }
    },
    {
      name: 'Without anthropic-version',
      url: `${PROXY_URL}/v1/messages`,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY
      }
    },
    {
      name: 'With User-Agent',
      url: `${PROXY_URL}/v1/messages`,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01',
        'User-Agent': 'claude-cli/1.0.0'
      }
    }
  ];

  for (const testCase of testCases) {
    console.log(`Testing: ${testCase.name}`);
    try {
      const response = await axios.post(testCase.url, {
        model: MODEL,
        max_tokens: 10,
        messages: [{ role: 'user', content: 'Hi' }]
      }, {
        headers: testCase.headers,
        timeout: 10000,
        validateStatus: () => true
      });

      console.log(`  ✅ Status: ${response.status}`);
      if (response.status === 200) {
        console.log(`  ✅ Success: ${response.data.content?.[0]?.text || 'Response received'}`);
      } else {
        console.log(`  ❌ Error: ${JSON.stringify(response.data)}`);
      }
    } catch (error) {
      console.log(`  ❌ Failed: ${error.message}`);
    }
    console.log('');
  }
}

async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--debug')) {
    await debugConnection();
  } else {
    await testClaudeCLICompatibility();
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testClaudeCLICompatibility, debugConnection };
