#!/usr/bin/env node

/**
 * Test script specifically for Claude CLI compatibility
 */

const axios = require('axios');

const PROXY_URL = process.env.ANTHROPIC_BASE_URL || 'http://localhost:3000';
const API_KEY = process.env.ANTHROPIC_AUTH_TOKEN || 'dummy';
const MODEL = process.env.ANTHROPIC_MODEL || 'gpt-4.1';

async function testClaudeCLICompatibility() {
  console.log('🔍 Testing Claude CLI compatibility...\n');
  console.log(`Base URL: ${PROXY_URL}`);
  console.log(`API Key: ${API_KEY}`);
  console.log(`Model: ${MODEL}\n`);

  // Test 1: Health check
  console.log('1. Testing health endpoint...');
  try {
    const response = await axios.get(`${PROXY_URL}/health`, { timeout: 5000 });
    console.log('✅ Health check passed');
  } catch (error) {
    console.log('❌ Health check failed:', error.message);
    return false;
  }

  // Test 2: Test with Claude CLI style headers
  console.log('\n2. Testing with Claude CLI style request...');
  try {
    const response = await axios.post(`${PROXY_URL}/v1/messages`, {
      model: MODEL,
      max_tokens: 50,
      messages: [
        {
          role: 'user',
          content: 'Say "Hello from Claude CLI test" and nothing else.'
        }
      ]
    }, {
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01',
        'User-Agent': 'claude-cli/test'
      },
      timeout: 30000
    });

    console.log('✅ Claude CLI style request successful');
    console.log('   Response:', response.data.content[0].text);
    console.log('   Model used:', response.data.model);
    console.log('   Stop reason:', response.data.stop_reason);
    
  } catch (error) {
    console.log('❌ Claude CLI style request failed');
    if (error.response) {
      console.log('   Status:', error.response.status);
      console.log('   Error:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.log('   Error:', error.message);
    }
    return false;
  }

  // Test 3: Test streaming (like Claude CLI might use)
  console.log('\n3. Testing streaming compatibility...');
  try {
    const response = await axios.post(`${PROXY_URL}/v1/messages`, {
      model: MODEL,
      max_tokens: 30,
      stream: true,
      messages: [
        {
          role: 'user',
          content: 'Count to 3'
        }
      ]
    }, {
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01',
        'Accept': 'text/event-stream'
      },
      responseType: 'stream',
      timeout: 30000
    });

    console.log('✅ Streaming request initiated');
    
    let eventCount = 0;
    response.data.on('data', (chunk) => {
      const lines = chunk.toString().split('\n');
      for (const line of lines) {
        if (line.startsWith('event:')) {
          eventCount++;
        }
      }
    });

    await new Promise((resolve) => {
      response.data.on('end', () => {
        console.log(`   Received ${eventCount} events`);
        resolve();
      });
    });

  } catch (error) {
    console.log('❌ Streaming test failed');
    console.log('   Error:', error.message);
    return false;
  }

  console.log('\n🎉 All Claude CLI compatibility tests passed!');
  console.log('\n📋 Try this command:');
  console.log(`export ANTHROPIC_BASE_URL=${PROXY_URL} ANTHROPIC_AUTH_TOKEN=${API_KEY} ANTHROPIC_MODEL=${MODEL} ANTHROPIC_SMALL_FAST_MODEL=gpt-4.1-mini && claude`);
  
  return true;
}

async function debugConnection() {
  console.log('🔧 Debug mode - checking connection details...\n');
  
  try {
    // Test basic connectivity
    const response = await axios.get(`${PROXY_URL}/health`, { 
      timeout: 5000,
      validateStatus: () => true // Accept any status code
    });
    
    console.log('Connection details:');
    console.log('  Status:', response.status);
    console.log('  Headers:', JSON.stringify(response.headers, null, 2));
    console.log('  Data:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.log('Connection error details:');
    console.log('  Code:', error.code);
    console.log('  Message:', error.message);
    console.log('  Stack:', error.stack);
    
    if (error.response) {
      console.log('  Response status:', error.response.status);
      console.log('  Response headers:', error.response.headers);
      console.log('  Response data:', error.response.data);
    }
  }
}

async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--debug')) {
    await debugConnection();
  } else {
    await testClaudeCLICompatibility();
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testClaudeCLICompatibility, debugConnection };
