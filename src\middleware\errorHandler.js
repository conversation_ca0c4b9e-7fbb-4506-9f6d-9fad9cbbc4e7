const logger = require('../utils/logger');

/**
 * Maps OpenAI error types to Anthropic error types
 */
const mapErrorType = (openaiErrorType) => {
  const errorMap = {
    'invalid_request_error': 'invalid_request_error',
    'authentication_error': 'authentication_error',
    'permission_denied_error': 'permission_error',
    'rate_limit_error': 'rate_limit_error',
    'internal_server_error': 'api_error',
    'server_error': 'api_error'
  };
  
  return errorMap[openaiErrorType] || 'api_error';
};

/**
 * Maps HTTP status codes to appropriate error types
 */
const mapStatusToErrorType = (status) => {
  const statusMap = {
    400: 'invalid_request_error',
    401: 'authentication_error',
    403: 'permission_error',
    429: 'rate_limit_error',
    500: 'api_error',
    502: 'api_error',
    503: 'api_error',
    504: 'api_error'
  };
  
  return statusMap[status] || 'api_error';
};

/**
 * Error handler middleware that formats errors in Anthropic API format
 */
const errorHandler = (err, req, res, next) => {
  logger.error('Error occurred:', {
    error: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    headers: req.headers,
    body: req.body
  });

  let status = 500;
  let errorType = 'api_error';
  let message = 'An unexpected error occurred';

  // Handle different error types
  if (err.response) {
    // Axios error with response
    status = err.response.status;
    errorType = mapStatusToErrorType(status);
    
    if (err.response.data?.error) {
      // OpenAI API error
      const openaiError = err.response.data.error;
      errorType = mapErrorType(openaiError.type);
      message = openaiError.message || message;
    } else {
      message = err.response.data?.message || err.message;
    }
  } else if (err.request) {
    // Network error
    status = 502;
    errorType = 'api_error';
    message = 'Failed to connect to OpenAI API';
  } else if (err.status) {
    // Custom error with status
    status = err.status;
    errorType = mapStatusToErrorType(status);
    message = err.message;
  } else {
    // Generic error
    message = err.message;
  }

  // Format error response in Anthropic style
  const errorResponse = {
    type: 'error',
    error: {
      type: errorType,
      message: message
    }
  };

  res.status(status).json(errorResponse);
};

module.exports = { errorHandler, mapErrorType, mapStatusToErrorType };
