{"level":"info","message":"OpenAI-Anthropic Proxy Server started on localhost:3000","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:21:10.345Z"}
{"features":{"streaming":true,"token_counting":true,"tool_calling":true,"vision":true},"level":"info","message":"Configuration loaded:","model_mapping_enabled":false,"openai_base_url":"https://api.openai.com/v1","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:21:10.350Z"}
{"level":"info","message":"OpenAI-Anthropic Proxy Server started on localhost:3000","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:26:27.014Z"}
{"features":{"streaming":true,"token_counting":true,"tool_calling":true,"vision":true},"level":"info","message":"Configuration loaded:","model_mapping_enabled":false,"openai_base_url":"https://api.openai.com/v1","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:26:27.018Z"}
{"body":{},"headers":{"accept":"application/json, text/plain, */*","accept-encoding":"gzip, compress, deflate, br","connection":"keep-alive","host":"localhost:3000","user-agent":"axios/1.10.0","x-api-key":"dummy"},"level":"info","message":"GET /v1/models","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:26:44.223Z"}
{"level":"info","message":"Fetching models list from OpenAI","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:26:44.225Z"}
{"body":{},"headers":{"accept":"application/json, text/plain, */*","accept-encoding":"gzip, compress, deflate, br","connection":"keep-alive","host":"localhost:3000","user-agent":"axios/1.10.0"},"level":"info","message":"GET /health","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:29:31.551Z"}
{"body":{},"headers":{"accept":"application/json, text/plain, */*","accept-encoding":"gzip, compress, deflate, br","connection":"keep-alive","host":"localhost:3000","user-agent":"axios/1.10.0","x-api-key":"dummy"},"level":"info","message":"GET /v1/models","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:29:31.561Z"}
{"level":"info","message":"Fetching models list from OpenAI","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:29:31.562Z"}
{"body":{"max_tokens":10,"messages":[{"content":"Hi","role":"user"}],"model":"gpt-4o-mini"},"headers":{"accept":"application/json, text/plain, */*","accept-encoding":"gzip, compress, deflate, br","anthropic-version":"2023-06-01","connection":"keep-alive","content-length":"83","content-type":"application/json","host":"localhost:3000","user-agent":"axios/1.10.0","x-api-key":"dummy"},"level":"info","message":"POST /v1/messages","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:29:32.153Z"}
{"level":"info","message":"Received message request","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:29:32.154Z"}
{"level":"info","message":"OpenAI-Anthropic Proxy Server started on localhost:3000","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:30:13.986Z"}
{"features":{"streaming":true,"token_counting":true,"tool_calling":true,"vision":true},"level":"info","message":"Configuration loaded:","model_mapping_enabled":false,"openai_base_url":"https://api.openai.com/v1","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:30:13.990Z"}
{"body":{},"headers":{"accept":"application/json, text/plain, */*","accept-encoding":"gzip, compress, deflate, br","connection":"keep-alive","host":"localhost:3000","user-agent":"axios/1.10.0"},"level":"info","message":"GET /health","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:30:46.066Z"}
{"body":{"max_tokens":50,"messages":[{"content":"Say \"Hello from Claude CLI test\" and nothing else.","role":"user"}],"model":"gpt-4.1"},"headers":{"accept":"application/json, text/plain, */*","accept-encoding":"gzip, compress, deflate, br","anthropic-version":"2023-06-01","connection":"keep-alive","content-length":"129","content-type":"application/json","host":"localhost:3000","user-agent":"claude-cli/test","x-api-key":"dummy"},"level":"info","message":"POST /v1/messages","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:30:46.090Z"}
{"level":"info","message":"Received message request","requestId":"req_9d6e4f8a-40ef-4856-8e1d-688c7b12b569","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:30:46.093Z"}
{"body":{"max_tokens":30,"messages":[{"content":"Count to 3","role":"user"}],"model":"gpt-4.1","stream":true},"headers":{"accept":"text/event-stream","accept-encoding":"gzip, compress, deflate, br","anthropic-version":"2023-06-01","connection":"keep-alive","content-length":"101","content-type":"application/json","host":"localhost:3000","user-agent":"axios/1.10.0","x-api-key":"dummy"},"level":"info","message":"POST /v1/messages","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:30:46.611Z"}
{"level":"info","message":"Received message request","requestId":"req_07f95ded-49a3-41ca-880c-1ea5cc4418a7","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:30:46.613Z"}
{"body":{},"headers":{"accept":"application/json, text/plain, */*","accept-encoding":"gzip, compress, deflate, br","connection":"keep-alive","host":"localhost:3000","user-agent":"axios/1.10.0"},"level":"info","message":"GET /health","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:31:33.187Z"}
{"body":{},"headers":{"accept":"application/json, text/plain, */*","accept-encoding":"gzip, compress, deflate, br","connection":"keep-alive","host":"localhost:3000","user-agent":"axios/1.10.0"},"level":"info","message":"GET /health","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:35:38.424Z"}
{"body":{"max_tokens":10,"messages":[{"content":"Hi","role":"user"}],"model":"gpt-4.1"},"headers":{"accept":"application/json, text/plain, */*","accept-encoding":"gzip, compress, deflate, br","anthropic-version":"2023-06-01","connection":"keep-alive","content-length":"79","content-type":"application/json","host":"localhost:3000","user-agent":"axios/1.10.0","x-api-key":"dummy"},"level":"info","message":"POST /v1/messages","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:35:38.437Z"}
{"level":"info","message":"Received message request","requestId":"req_71d87fb8-7a48-49fb-b906-a313566c50b0","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:35:38.438Z"}
{"body":{"max_tokens":10,"messages":[{"content":"Hi","role":"user"}],"model":"gpt-4.1"},"headers":{"accept":"application/json, text/plain, */*","accept-encoding":"gzip, compress, deflate, br","anthropic-version":"2023-06-01","authorization":"Bearer dummy","connection":"keep-alive","content-length":"79","content-type":"application/json","host":"localhost:3000","user-agent":"axios/1.10.0"},"level":"info","message":"POST /v1/messages","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:35:39.227Z"}
{"level":"info","message":"Received message request","requestId":"req_d74b7139-5df5-476d-8585-67083adb55d9","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:35:39.230Z"}
{"body":{"max_tokens":10,"messages":[{"content":"Hi","role":"user"}],"model":"gpt-4.1"},"headers":{"accept":"application/json, text/plain, */*","accept-encoding":"gzip, compress, deflate, br","connection":"keep-alive","content-length":"79","content-type":"application/json","host":"localhost:3000","user-agent":"axios/1.10.0","x-api-key":"dummy"},"level":"info","message":"POST /v1/messages","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:35:39.690Z"}
{"level":"info","message":"Received message request","requestId":"req_deb2b404-ad07-490a-914e-fe8a5d2e97f4","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:35:39.692Z"}
{"body":{"max_tokens":10,"messages":[{"content":"Hi","role":"user"}],"model":"gpt-4.1"},"headers":{"accept":"application/json, text/plain, */*","accept-encoding":"gzip, compress, deflate, br","anthropic-version":"2023-06-01","connection":"keep-alive","content-length":"79","content-type":"application/json","host":"localhost:3000","user-agent":"claude-cli/1.0.0","x-api-key":"dummy"},"level":"info","message":"POST /v1/messages","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:35:40.139Z"}
{"level":"info","message":"Received message request","requestId":"req_99c6aed7-41de-4e3e-809b-50391c07ba7c","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:35:40.141Z"}
{"level":"info","message":"OpenAI-Anthropic Proxy Server started on localhost:3000","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:36:08.810Z"}
{"features":{"streaming":true,"token_counting":true,"tool_calling":true,"vision":true},"level":"info","message":"Configuration loaded:","model_mapping_enabled":false,"openai_base_url":"https://api.openai.com/v1","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:36:08.815Z"}
{"level":"info","message":"OpenAI-Anthropic Proxy Server started on localhost:3000","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:45:03.820Z"}
{"features":{"streaming":true,"token_counting":true,"tool_calling":true,"vision":true},"level":"info","message":"Configuration loaded:","model_mapping_enabled":false,"openai_base_url":"https://api.openai.com/v1","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:45:03.824Z"}
{"level":"info","message":"OpenAI-Anthropic Proxy Server started on localhost:3000","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:54:18.163Z"}
{"features":{"streaming":true,"token_counting":true,"tool_calling":true,"vision":true},"level":"info","message":"Configuration loaded:","model_mapping_enabled":false,"openai_base_url":"https://api.openai.com/v1","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:54:18.168Z"}
{"body":{},"headers":{"accept":"application/json, text/plain, */*","accept-encoding":"gzip, compress, deflate, br","connection":"keep-alive","host":"localhost:3000","user-agent":"axios/1.10.0"},"ip":"::1","level":"info","message":"GET /health","query":{},"service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:55:03.790Z","userAgent":"axios/1.10.0"}
{"body":{"max_tokens":50,"messages":[{"content":"Say \"Hello from Claude CLI test\" and nothing else.","role":"user"}],"model":"gpt-4.1"},"headers":{"accept":"application/json, text/plain, */*","accept-encoding":"gzip, compress, deflate, br","anthropic-version":"2023-06-01","connection":"keep-alive","content-length":"129","content-type":"application/json","host":"localhost:3000","user-agent":"claude-cli/test","x-api-key":"dummy"},"ip":"::1","level":"info","message":"POST /v1/messages","query":{},"service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:55:03.817Z","userAgent":"claude-cli/test"}
{"level":"info","message":"Received message request","requestId":"req_bbaa5116-0b99-4982-8850-b28284f2fc46","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:55:03.819Z"}
{"body":{"max_tokens":30,"messages":[{"content":"Count to 3","role":"user"}],"model":"gpt-4.1","stream":true},"headers":{"accept":"text/event-stream","accept-encoding":"gzip, compress, deflate, br","anthropic-version":"2023-06-01","connection":"keep-alive","content-length":"101","content-type":"application/json","host":"localhost:3000","user-agent":"axios/1.10.0","x-api-key":"dummy"},"ip":"::1","level":"info","message":"POST /v1/messages","query":{},"service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:55:04.437Z","userAgent":"axios/1.10.0"}
{"level":"info","message":"Received message request","requestId":"req_30d75c2d-7485-44e9-b9e8-02cac39db8f3","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:55:04.438Z"}
{"body":{"max_tokens":100,"messages":[{"content":"What time is it? Use the get_time tool.","role":"user"}],"model":"gpt-4.1","tool_choice":{"type":"auto"},"tools":[{"description":"Get the current time","input_schema":{"properties":{},"required":[],"type":"object"},"name":"get_time"}]},"headers":{"accept":"application/json, text/plain, */*","accept-encoding":"gzip, compress, deflate, br","anthropic-version":"2023-06-01","connection":"keep-alive","content-length":"277","content-type":"application/json","host":"localhost:3000","user-agent":"axios/1.10.0","x-api-key":"dummy"},"ip":"::1","level":"info","message":"POST /v1/messages","query":{},"service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:55:04.879Z","userAgent":"axios/1.10.0"}
{"level":"info","message":"Received message request","requestId":"req_5cfab7b0-6320-4b74-933f-59ff99ba436b","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:55:04.880Z"}
