{"level":"info","message":"OpenAI-Anthropic Proxy Server started on localhost:3000","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:21:10.345Z"}
{"features":{"streaming":true,"token_counting":true,"tool_calling":true,"vision":true},"level":"info","message":"Configuration loaded:","model_mapping_enabled":false,"openai_base_url":"https://api.openai.com/v1","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:21:10.350Z"}
{"level":"info","message":"OpenAI-Anthropic Proxy Server started on localhost:3000","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:26:27.014Z"}
{"features":{"streaming":true,"token_counting":true,"tool_calling":true,"vision":true},"level":"info","message":"Configuration loaded:","model_mapping_enabled":false,"openai_base_url":"https://api.openai.com/v1","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:26:27.018Z"}
{"body":{},"headers":{"accept":"application/json, text/plain, */*","accept-encoding":"gzip, compress, deflate, br","connection":"keep-alive","host":"localhost:3000","user-agent":"axios/1.10.0","x-api-key":"dummy"},"level":"info","message":"GET /v1/models","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:26:44.223Z"}
{"level":"info","message":"Fetching models list from OpenAI","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:26:44.225Z"}
{"body":{},"headers":{"accept":"application/json, text/plain, */*","accept-encoding":"gzip, compress, deflate, br","connection":"keep-alive","host":"localhost:3000","user-agent":"axios/1.10.0"},"level":"info","message":"GET /health","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:29:31.551Z"}
{"body":{},"headers":{"accept":"application/json, text/plain, */*","accept-encoding":"gzip, compress, deflate, br","connection":"keep-alive","host":"localhost:3000","user-agent":"axios/1.10.0","x-api-key":"dummy"},"level":"info","message":"GET /v1/models","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:29:31.561Z"}
{"level":"info","message":"Fetching models list from OpenAI","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:29:31.562Z"}
{"body":{"max_tokens":10,"messages":[{"content":"Hi","role":"user"}],"model":"gpt-4o-mini"},"headers":{"accept":"application/json, text/plain, */*","accept-encoding":"gzip, compress, deflate, br","anthropic-version":"2023-06-01","connection":"keep-alive","content-length":"83","content-type":"application/json","host":"localhost:3000","user-agent":"axios/1.10.0","x-api-key":"dummy"},"level":"info","message":"POST /v1/messages","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:29:32.153Z"}
{"level":"info","message":"Received message request","service":"openai-anthropic-proxy","timestamp":"2025-06-24T20:29:32.154Z"}
