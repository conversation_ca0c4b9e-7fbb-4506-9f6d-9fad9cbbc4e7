#!/usr/bin/env node

/**
 * Debug script to test JSON generation
 */

const RequestTranslator = require('./src/translators/requestTranslator');

// Mock config
const config = {
  model_mapping: {
    enabled: false
  }
};

const translator = new RequestTranslator(config);

// Test simple request
const simpleRequest = {
  model: 'gpt-4o',
  max_tokens: 10,
  messages: [
    {
      role: 'user',
      content: 'Hello'
    }
  ]
};

console.log('🔍 Testing simple request translation...');
console.log('Input:', JSON.stringify(simpleRequest, null, 2));

try {
  const translated = translator.translateMessagesRequest(simpleRequest);
  console.log('\n✅ Translation successful:');
  console.log(JSON.stringify(translated, null, 2));
  
  // Test if it's valid JSON
  const jsonString = JSON.stringify(translated);
  JSON.parse(jsonString);
  console.log('\n✅ Generated valid JSON');
  
} catch (error) {
  console.log('\n❌ Translation failed:');
  console.log('Error:', error.message);
  console.log('Stack:', error.stack);
}

// Test complex request with tools
const complexRequest = {
  model: 'gpt-4o',
  max_tokens: 100,
  tools: [
    {
      name: 'get_weather',
      description: 'Get weather information',
      input_schema: {
        type: 'object',
        properties: {
          location: { type: 'string' }
        },
        required: ['location']
      }
    }
  ],
  tool_choice: { type: 'auto' },
  messages: [
    {
      role: 'user',
      content: [
        {
          type: 'text',
          text: 'What is the weather in San Francisco?'
        }
      ]
    }
  ]
};

console.log('\n🔍 Testing complex request with tools...');
console.log('Input:', JSON.stringify(complexRequest, null, 2));

try {
  const translated = translator.translateMessagesRequest(complexRequest);
  console.log('\n✅ Complex translation successful:');
  console.log(JSON.stringify(translated, null, 2));
  
  // Test if it's valid JSON
  const jsonString = JSON.stringify(translated);
  JSON.parse(jsonString);
  console.log('\n✅ Generated valid JSON');
  
} catch (error) {
  console.log('\n❌ Complex translation failed:');
  console.log('Error:', error.message);
  console.log('Stack:', error.stack);
}

// Test edge cases
const edgeCases = [
  {
    name: 'Empty content array',
    request: {
      model: 'gpt-4o',
      max_tokens: 10,
      messages: [
        {
          role: 'user',
          content: []
        }
      ]
    }
  },
  {
    name: 'Null content',
    request: {
      model: 'gpt-4o',
      max_tokens: 10,
      messages: [
        {
          role: 'user',
          content: null
        }
      ]
    }
  },
  {
    name: 'Undefined properties',
    request: {
      model: 'gpt-4o',
      max_tokens: 10,
      messages: [
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: undefined
            }
          ]
        }
      ]
    }
  }
];

for (const testCase of edgeCases) {
  console.log(`\n🔍 Testing edge case: ${testCase.name}...`);
  try {
    const translated = translator.translateMessagesRequest(testCase.request);
    const jsonString = JSON.stringify(translated);
    JSON.parse(jsonString);
    console.log(`✅ ${testCase.name} - Valid JSON generated`);
  } catch (error) {
    console.log(`❌ ${testCase.name} - Failed:`, error.message);
  }
}
