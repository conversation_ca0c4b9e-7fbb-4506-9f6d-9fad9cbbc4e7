#!/usr/bin/env node

/**
 * Troubleshooting script for Claude CLI connection issues
 */

const axios = require('axios');

const PROXY_URL = process.env.ANTHROPIC_BASE_URL || 'http://localhost:4141';
const API_KEY = process.env.ANTHROPIC_AUTH_TOKEN || 'dummy';

async function checkConnection() {
  console.log('🔍 Troubleshooting Claude CLI connection...\n');
  
  // Test 1: Check if server is running
  console.log('1. Testing server connection...');
  try {
    const response = await axios.get(`${PROXY_URL}/health`, { timeout: 5000 });
    console.log('✅ Server is running');
    console.log('   Response:', response.data);
  } catch (error) {
    console.log('❌ Server connection failed');
    console.log('   Error:', error.code || error.message);
    console.log('   Make sure to run: npm start');
    return false;
  }

  // Test 2: Check models endpoint
  console.log('\n2. Testing models endpoint...');
  try {
    const response = await axios.get(`${PROXY_URL}/v1/models`, {
      headers: { 'x-api-key': API_KEY },
      timeout: 10000
    });
    console.log('✅ Models endpoint working');
    console.log('   Available models:', response.data.data.slice(0, 5).map(m => m.id));
    if (response.data.data.length > 5) {
      console.log(`   ... and ${response.data.data.length - 5} more models`);
    }
  } catch (error) {
    console.log('❌ Models endpoint failed');
    console.log('   Error:', error.response?.data || error.message);
    console.log('   Check your OpenAI API key in config.json');
    return false;
  }

  // Test 3: Test message endpoint
  console.log('\n3. Testing message endpoint...');
  try {
    const response = await axios.post(`${PROXY_URL}/v1/messages`, {
      model: 'gpt-4o-mini',
      max_tokens: 10,
      messages: [{ role: 'user', content: 'Hi' }]
    }, {
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01'
      },
      timeout: 30000
    });
    console.log('✅ Message endpoint working');
    console.log('   Response:', response.data.content[0].text.substring(0, 50) + '...');
  } catch (error) {
    console.log('❌ Message endpoint failed');
    console.log('   Error:', error.response?.data || error.message);
    return false;
  }

  console.log('\n🎉 All tests passed! Your proxy is working correctly.');
  console.log('\n📋 Recommended environment variables:');
  console.log(`export ANTHROPIC_BASE_URL=${PROXY_URL}`);
  console.log(`export ANTHROPIC_AUTH_TOKEN=${API_KEY}`);
  console.log('export ANTHROPIC_MODEL=gpt-4o');
  console.log('export ANTHROPIC_SMALL_FAST_MODEL=gpt-4o-mini');
  
  return true;
}

async function checkModels() {
  console.log('\n🔍 Checking available models...');
  try {
    const response = await axios.get(`${PROXY_URL}/v1/models`, {
      headers: { 'x-api-key': API_KEY }
    });
    
    const models = response.data.data;
    console.log(`\n📋 Found ${models.length} available models:`);
    
    // Group models by type
    const chatModels = models.filter(m => m.id.includes('gpt'));
    const embeddingModels = models.filter(m => m.id.includes('embedding'));
    const otherModels = models.filter(m => !m.id.includes('gpt') && !m.id.includes('embedding'));
    
    if (chatModels.length > 0) {
      console.log('\n💬 Chat Models:');
      chatModels.forEach(model => {
        console.log(`   - ${model.id} (${model.display_name})`);
      });
    }
    
    if (embeddingModels.length > 0) {
      console.log('\n🔤 Embedding Models:');
      embeddingModels.slice(0, 3).forEach(model => {
        console.log(`   - ${model.id} (${model.display_name})`);
      });
      if (embeddingModels.length > 3) {
        console.log(`   ... and ${embeddingModels.length - 3} more`);
      }
    }
    
    if (otherModels.length > 0) {
      console.log('\n🎨 Other Models:');
      otherModels.slice(0, 3).forEach(model => {
        console.log(`   - ${model.id} (${model.display_name})`);
      });
      if (otherModels.length > 3) {
        console.log(`   ... and ${otherModels.length - 3} more`);
      }
    }
    
  } catch (error) {
    console.log('❌ Failed to fetch models');
    console.log('   Error:', error.response?.data || error.message);
  }
}

async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--models')) {
    await checkModels();
  } else {
    const success = await checkConnection();
    if (success && args.includes('--verbose')) {
      await checkModels();
    }
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { checkConnection, checkModels };
