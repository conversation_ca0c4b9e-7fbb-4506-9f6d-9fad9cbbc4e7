#!/usr/bin/env node

/**
 * Debug server to capture exactly what <PERSON> is sending
 */

const express = require('express');
const app = express();
const port = 4141;

// Middleware to capture raw body
app.use(express.raw({ type: '*/*', limit: '10mb' }));

// Middleware to parse JSON when possible
app.use((req, res, next) => {
  if (req.headers['content-type'] === 'application/json' && req.body) {
    try {
      req.jsonBody = JSON.parse(req.body.toString());
    } catch (e) {
      req.jsonBody = null;
    }
  }
  next();
});

app.use((req, res, next) => {
  console.log('\n🔍 === CLAUDE CLI REQUEST ===');
  console.log('Timestamp:', new Date().toISOString());
  console.log('Method:', req.method);
  console.log('URL:', req.url);
  console.log('Query:', req.query);
  console.log('Headers:');
  Object.entries(req.headers).forEach(([key, value]) => {
    console.log(`  ${key}: ${value}`);
  });

  console.log('Body (raw length):', req.body.length);
  console.log('Body (string):', req.body.toString());

  if (req.jsonBody) {
    console.log('Body (parsed JSON):');
    console.log(JSON.stringify(req.jsonBody, null, 2));
  }

  console.log('=== END REQUEST ===\n');

  // Send a proper Anthropic-style response for /v1/messages
  if (req.url === '/v1/messages' && req.method === 'POST') {
    res.status(200).json({
      id: 'msg_debug123',
      type: 'message',
      role: 'assistant',
      content: [
        {
          type: 'text',
          text: 'Debug response from debug server. Your request was captured successfully!'
        }
      ],
      model: req.jsonBody?.model || 'debug-model',
      stop_reason: 'end_turn',
      stop_sequence: null,
      usage: {
        input_tokens: 10,
        output_tokens: 15,
        cache_creation_input_tokens: 0,
        cache_read_input_tokens: 0
      }
    });
  } else if (req.url === '/v1/models' && req.method === 'GET') {
    res.status(200).json({
      data: [
        {
          id: 'gpt-4o',
          type: 'model',
          display_name: 'GPT-4o',
          created_at: new Date().toISOString()
        }
      ],
      has_more: false
    });
  } else {
    // Generic response for other endpoints
    res.status(200).json({
      message: 'Debug server received your request',
      method: req.method,
      url: req.url,
      timestamp: new Date().toISOString()
    });
  }
});

app.listen(port, () => {
  console.log(`🔧 Debug server running on http://localhost:${port}`);
  console.log('This will capture exactly what Claude CLI sends.');
  console.log('\nTo test with Claude CLI, run:');
  console.log(`export ANTHROPIC_BASE_URL=http://localhost:${port} ANTHROPIC_AUTH_TOKEN=dummy ANTHROPIC_MODEL=gpt-4o && claude`);
  console.log('\nOr test with curl:');
  console.log(`curl -X POST http://localhost:${port}/v1/messages -H "Content-Type: application/json" -H "x-api-key: dummy" -d '{"model":"gpt-4o","max_tokens":10,"messages":[{"role":"user","content":"test"}]}'`);
  console.log('\nPress Ctrl+C to stop');
});
