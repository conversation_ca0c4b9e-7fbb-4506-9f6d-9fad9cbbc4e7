#!/usr/bin/env node

/**
 * Debug server to capture exactly what <PERSON> is sending
 */

const express = require('express');
const app = express();
const port = 3001;

// Middleware to log everything
app.use(express.raw({ type: '*/*', limit: '10mb' }));

app.use((req, res, next) => {
  console.log('\n🔍 === INCOMING REQUEST ===');
  console.log('Method:', req.method);
  console.log('URL:', req.url);
  console.log('Headers:', JSON.stringify(req.headers, null, 2));
  console.log('Body (raw):', req.body.toString());
  
  try {
    const bodyStr = req.body.toString();
    if (bodyStr && bodyStr.startsWith('{')) {
      console.log('Body (parsed):', JSON.stringify(JSON.parse(bodyStr), null, 2));
    }
  } catch (e) {
    console.log('Body (not JSON):', req.body.toString());
  }
  
  console.log('=== END REQUEST ===\n');
  
  // Send a simple response
  res.status(200).json({
    message: 'Debug server received your request',
    method: req.method,
    url: req.url,
    timestamp: new Date().toISOString()
  });
});

app.listen(port, () => {
  console.log(`🔧 Debug server running on http://localhost:${port}`);
  console.log('This will capture exactly what Claude CLI sends.');
  console.log('\nTo test with Claude CLI, use:');
  console.log(`export ANTHROPIC_BASE_URL=http://localhost:${port} ANTHROPIC_AUTH_TOKEN=dummy ANTHROPIC_MODEL=gpt-4o && claude`);
  console.log('\nPress Ctrl+C to stop');
});
